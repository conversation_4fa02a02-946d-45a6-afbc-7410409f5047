package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/cloudwego/eino/components/network"
	"github.com/cloudwego/eino/components/network/schema"
)

func main() {
	fmt.Println("=== 测试Eino网络分析组件的高级功能 ===\n")

	ctx := context.Background()

	// 1. 测试基础网络分析器
	fmt.Println("🔍 1. 测试基础网络分析功能...")
	analyzer := network.NewAnalyzer(
		network.WithGeoLocation(true),
		network.WithTLSSecurity(true),
		network.WithFingerprint(true),
		network.WithWHOISLookup(true),
		network.WithCDNDetection(true),
		network.WithTimeout(30*time.Second),
	)

	result, err := analyzer.Analyze(ctx, "example.com")
	if err != nil {
		log.Printf("基础分析失败: %v", err)
	} else {
		fmt.Printf("✅ 基础分析成功: %s -> %s (耗时: %dms)\n",
			result.Domain, result.IP, result.AnalysisTime)
	}

	// 2. 测试指标收集
	fmt.Println("\n📊 2. 测试指标收集功能...")
	metricsCollector := network.NewMetricsCollector(network.DefaultMetricsConfig())

	// 模拟一些指标
	metricsCollector.RecordRequest()
	metricsCollector.RecordSuccess(100 * time.Millisecond)
	metricsCollector.RecordModuleAnalysis("dns")
	metricsCollector.RecordModuleAnalysis("whois")
	metricsCollector.RecordCacheHit()
	metricsCollector.RecordSecurityScore(85)

	snapshot := metricsCollector.GetSnapshot()
	fmt.Printf("✅ 指标收集成功:\n")
	fmt.Printf("   总请求数: %d\n", snapshot.TotalRequests)
	fmt.Printf("   成功率: %.2f%%\n", snapshot.SuccessRate)
	fmt.Printf("   平均响应时间: %.2f ms\n", snapshot.AvgResponseTime)
	fmt.Printf("   缓存命中率: %.2f%%\n", snapshot.CacheHitRate)

	// 3. 测试AI威胁分析
	fmt.Println("\n🤖 3. 测试AI威胁分析功能...")
	securityModel := network.NewMockSecurityModel()
	aiAnalyzer := network.NewAIAnalyzer(network.DefaultAIConfig(), securityModel)

	if result != nil {
		threatAnalysis, err := aiAnalyzer.AnalyzeWithAI(ctx, result)
		if err != nil {
			log.Printf("AI分析失败: %v", err)
		} else {
			fmt.Printf("✅ AI威胁分析成功:\n")
			fmt.Printf("   威胁等级: %s\n", threatAnalysis.ThreatLevel)
			fmt.Printf("   风险评分: %d\n", threatAnalysis.RiskScore)
			fmt.Printf("   置信度: %.2f\n", threatAnalysis.Confidence)
			fmt.Printf("   威胁类型: %v\n", threatAnalysis.ThreatTypes)

			// 生成安全报告
			securityReport, err := aiAnalyzer.GenerateSecurityReport(ctx, threatAnalysis)
			if err != nil {
				log.Printf("报告生成失败: %v", err)
			} else {
				fmt.Printf("✅ 安全报告生成成功:\n")
				fmt.Printf("   摘要: %s\n", securityReport.Summary)
				fmt.Printf("   安全评分: %d\n", securityReport.SecurityScore)
				fmt.Printf("   建议数量: %d\n", len(securityReport.Recommendations))
			}
		}
	}

	// 4. 测试链式分析
	fmt.Println("\n🔗 4. 测试链式分析功能...")
	chainConfig := network.DefaultChainConfig()
	chainAnalyzer := network.NewChainableNetworkAnalyzer(analyzer, aiAnalyzer, chainConfig)

	chainInput := &network.ChainInput{
		URL:      "github.com",
		Metadata: map[string]string{"source": "test"},
		Options:  []network.Option{},
	}

	chainOutput, err := chainAnalyzer.Invoke(ctx, chainInput)
	if err != nil {
		log.Printf("链式分析失败: %v", err)
	} else {
		fmt.Printf("✅ 链式分析成功:\n")
		fmt.Printf("   整体成功: %v\n", chainOutput.Success)
		if chainOutput.NetworkResult != nil {
			fmt.Printf("   域名: %s\n", chainOutput.NetworkResult.Domain)
			fmt.Printf("   IP: %s\n", chainOutput.NetworkResult.IP)
		}
		if chainOutput.ThreatAnalysis != nil {
			fmt.Printf("   威胁等级: %s\n", chainOutput.ThreatAnalysis.ThreatLevel)
		}
		fmt.Printf("   链式元数据: %v\n", chainOutput.ChainMetadata)
	}

	// 5. 测试流式分析
	fmt.Println("\n🌊 5. 测试流式分析功能...")
	streamConfig := network.DefaultStreamConfig()
	streamConfig.BufferSize = 100
	streamConfig.WorkerCount = 2
	streamAnalyzer := network.NewStreamAnalyzer(analyzer, streamConfig)

	// 启动流式分析器
	if err := streamAnalyzer.Start(ctx); err != nil {
		log.Printf("启动流式分析器失败: %v", err)
	} else {
		fmt.Println("✅ 流式分析器启动成功")

		// 提交一些URL进行测试
		testUrls := []string{"example.com", "google.com"}
		for _, url := range testUrls {
			if err := streamAnalyzer.Submit(url); err != nil {
				log.Printf("提交URL失败: %v", err)
			} else {
				fmt.Printf("   已提交: %s\n", url)
			}
		}

		// 收集结果
		resultCount := 0
		timeout := time.After(10 * time.Second)

	streamLoop:
		for {
			select {
			case result := <-streamAnalyzer.Results():
				resultCount++
				fmt.Printf("   收到结果 %d: %s (成功: %v)\n",
					result.Index, result.Result.Domain, result.Result.Success)

				if resultCount >= len(testUrls) {
					break streamLoop
				}

			case err := <-streamAnalyzer.Errors():
				fmt.Printf("   流式错误: %v\n", err)

			case <-timeout:
				fmt.Println("   流式分析超时")
				break streamLoop
			}
		}

		// 获取流式指标
		streamMetrics := streamAnalyzer.GetMetrics()
		fmt.Printf("✅ 流式分析完成:\n")
		fmt.Printf("   已处理: %d\n", streamMetrics.ProcessedCount)
		fmt.Printf("   错误数: %d\n", streamMetrics.ErrorCount)
		fmt.Printf("   缓冲区使用率: %.2f%%\n", streamMetrics.BufferUsage*100)

		// 停止流式分析器
		if err := streamAnalyzer.Stop(); err != nil {
			log.Printf("停止流式分析器失败: %v", err)
		} else {
			fmt.Println("✅ 流式分析器已停止")
		}
	}

	// 6. 测试批量分析
	fmt.Println("\n📦 6. 测试批量分析功能...")
	batchUrls := []string{"example.com", "google.com", "github.com"}

	batchResults, err := analyzer.BatchAnalyze(ctx, batchUrls)
	if err != nil {
		log.Printf("批量分析失败: %v", err)
	} else {
		successCount := 0
		for _, result := range batchResults {
			if result.Success {
				successCount++
			}
		}
		fmt.Printf("✅ 批量分析完成: %d/%d 成功\n", successCount, len(batchUrls))

		for i, result := range batchResults {
			fmt.Printf("   结果 %d: %s -> %s (成功: %v)\n",
				i+1, result.Domain, result.IP, result.Success)
		}
	}

	// 7. 测试自定义分析链
	fmt.Println("\n⛓️  7. 测试自定义分析链功能...")
	analysisChain := network.NewNetworkAnalysisChain()
	analysisChain.AddStep(network.NewNetworkAnalysisStep(analyzer))

	if aiAnalyzer != nil {
		analysisChain.AddStep(network.NewAIAnalysisStep(aiAnalyzer))
	}

	chainResult, err := analysisChain.Execute(ctx, "cloudflare.com")
	if err != nil {
		log.Printf("自定义链执行失败: %v", err)
	} else {
		fmt.Printf("✅ 自定义分析链执行成功\n")

		// 根据最终结果类型进行处理
		switch v := chainResult.(type) {
		case *network.ThreatAnalysis:
			fmt.Printf("   最终结果: AI威胁分析 (威胁等级: %s)\n", v.ThreatLevel)
		case *schema.NetworkResult:
			fmt.Printf("   最终结果: 网络分析 (域名: %s)\n", v.Domain)
		default:
			fmt.Printf("   最终结果类型: %T\n", v)
		}
	}

	fmt.Println("\n=== 高级功能测试完成 ===")
	fmt.Println("🎉 所有新功能都已成功集成到Eino框架中！")

	// 显示功能总结
	fmt.Println("\n📋 已实现的高级功能:")
	fmt.Println("✅ 流式处理集成 - 支持实时数据流分析")
	fmt.Println("✅ AI威胁分析 - 智能安全评估和威胁检测")
	fmt.Println("✅ 链式编排集成 - 支持复杂的分析工作流")
	fmt.Println("✅ 指标收集系统 - 完整的性能监控")
	fmt.Println("✅ 批量并发处理 - 高效的大规模分析")
	fmt.Println("✅ 模块化架构 - 易于扩展和维护")
	fmt.Println("✅ 错误处理机制 - 优雅降级和容错")
	fmt.Println("✅ 缓存优化 - 智能缓存提升性能")
}
