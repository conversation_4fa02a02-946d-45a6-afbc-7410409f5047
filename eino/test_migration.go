package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/cloudwego/eino/components/network"
)

func main() {
	fmt.Println("=== 测试MCP项目迁移到Eino的新功能 ===\n")

	// 创建网络分析器，启用所有新功能
	analyzer := network.NewAnalyzer(
		network.WithGeoLocation(true),    // 启用地理位置分析
		network.WithTLSSecurity(true),    // 启用TLS安全分析
		network.WithFingerprint(true),    // 启用网站指纹识别
		network.WithWHOISLookup(true),    // 启用WHOIS查询
		network.WithCDNDetection(true),   // 启用CDN检测
		network.WithTimeout(30*time.Second), // 设置30秒超时
	)

	ctx := context.Background()

	// 测试域名列表
	testDomains := []string{
		"example.com",
		"google.com",
		"github.com",
		"cloudflare.com",
	}

	fmt.Printf("正在分析 %d 个域名...\n\n", len(testDomains))

	for i, domain := range testDomains {
		fmt.Printf("=== 测试 %d: %s ===\n", i+1, domain)

		result, err := analyzer.Analyze(ctx, domain)
		if err != nil {
			log.Printf("分析失败: %v", err)
			continue
		}

		if !result.Success {
			fmt.Printf("❌ 分析失败: %s\n\n", result.Error)
			continue
		}

		fmt.Printf("✅ 分析成功 (耗时: %dms)\n", result.AnalysisTime)
		fmt.Printf("🌐 IP地址: %s\n", result.IP)

		// DNS信息
		if result.DNS != nil && len(result.DNS.ARecords) > 0 {
			fmt.Printf("📡 DNS记录: %d个A记录\n", len(result.DNS.ARecords))
		}

		// ASN信息
		if result.ASN != nil && result.ASN.ASN != "ERROR" {
			fmt.Printf("🏢 ASN: %s (%s)\n", result.ASN.ASN, result.ASN.ISP)
		}

		// WHOIS信息 (新功能)
		if result.WHOIS != nil {
			if result.WHOIS.Success {
				fmt.Printf("📋 WHOIS: 查询成功")
				if result.WHOIS.Company != nil && result.WHOIS.Company.CompanyName != "" {
					fmt.Printf(" (公司: %s)", result.WHOIS.Company.CompanyName)
				}
				fmt.Println()
			} else {
				fmt.Printf("📋 WHOIS: 查询失败\n")
			}
		}

		// CDN检测 (新功能)
		if result.CDN != nil {
			if result.CDN.Provider != "" {
				fmt.Printf("🚀 CDN: %s (置信度: %d%%)\n", result.CDN.Provider, result.CDN.Confidence)
			} else {
				fmt.Printf("🚀 CDN: 未检测到\n")
			}
		}

		// 高级分析
		if result.Advanced != nil {
			// 地理位置 (新功能)
			if result.Advanced.GeoLocation != nil {
				geo := result.Advanced.GeoLocation
				fmt.Printf("🌍 地理位置: %s, %s", geo.Country, geo.City)
				if geo.ISP != "" {
					fmt.Printf(" (ISP: %s)", geo.ISP)
				}
				fmt.Println()
			}

			// TLS安全分析
			if result.Advanced.TLSSecurity != nil {
				tls := result.Advanced.TLSSecurity
				fmt.Printf("🔒 TLS安全: %s", tls.SecurityRating)
				if len(tls.SupportedVersions) > 0 {
					fmt.Printf(" (版本: %v)", tls.SupportedVersions)
				}
				fmt.Println()
			}

			// 网站指纹识别 (新功能)
			if result.Advanced.Fingerprint != nil {
				fp := result.Advanced.Fingerprint
				fmt.Printf("🔍 技术栈:")
				if fp.ServerType != "" {
					fmt.Printf(" 服务器=%s", fp.ServerType)
				}
				if fp.CMS != "" {
					fmt.Printf(", CMS=%s", fp.CMS)
				}
				if len(fp.Frameworks) > 0 {
					fmt.Printf(", 框架=%v", fp.Frameworks)
				}
				if len(fp.Technologies) > 0 {
					fmt.Printf(", 技术=%v", fp.Technologies)
				}
				fmt.Printf(" (置信度: %d%%)\n", fp.Confidence)
			}
		}

		fmt.Println()
	}

	// 批量分析测试
	fmt.Println("=== 批量分析测试 ===")
	results, err := analyzer.BatchAnalyze(ctx, testDomains)
	if err != nil {
		log.Printf("批量分析失败: %v", err)
		return
	}

	successCount := 0
	for _, result := range results {
		if result != nil && result.Success {
			successCount++
		}
	}

	fmt.Printf("批量分析完成: %d/%d 成功\n", successCount, len(testDomains))

	fmt.Println("\n=== 迁移功能测试完成 ===")
	fmt.Println("✅ WHOIS查询模块")
	fmt.Println("✅ CDN检测模块")
	fmt.Println("✅ 地理位置模块")
	fmt.Println("✅ 网站指纹识别模块")
	fmt.Println("✅ 批量并发分析")
	fmt.Println("✅ 错误处理机制")
}
