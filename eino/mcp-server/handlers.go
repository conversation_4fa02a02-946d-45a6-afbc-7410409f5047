/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package main

import (
	"context"
	"fmt"
	"time"

	"github.com/cloudwego/eino/components/network"
	"github.com/cloudwego/eino/components/network/schema"
)

// handleAnalyzeDomain 处理域名分析请求
func (s *MCPServer) handleAnalyzeDomain(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	target := args["target"].(string)
	enableAI := getBoolArg(args, "enable_ai_analysis", true)
	includeReport := getBoolArg(args, "include_security_report", false)
	depth := getStringArg(args, "analysis_depth", "standard")

	// 记录到会话上下文
	session.Context["last_target"] = target
	session.Context["last_analysis_time"] = time.Now()

	// 根据分析深度配置分析器
	analyzer := s.configureAnalyzerByDepth(depth)

	// 执行网络分析
	result, err := analyzer.Analyze(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("网络分析失败: %w", err)
	}

	// 记录指标
	if s.metrics != nil {
		s.metrics.RecordModuleAnalysis("network")
		if result.Success {
			s.metrics.RecordSuccess(time.Duration(result.AnalysisTime) * time.Millisecond)
		} else {
			s.metrics.RecordFailure()
		}
	}

	// 构建响应
	response := map[string]interface{}{
		"target":          target,
		"analysis_result": result,
		"analysis_depth":  depth,
		"timestamp":       time.Now(),
	}

	// AI威胁分析
	if enableAI && s.aiAnalyzer != nil && result.Success {
		threatAnalysis, err := s.aiAnalyzer.AnalyzeWithAI(ctx, result)
		if err != nil {
			response["ai_analysis_error"] = err.Error()
		} else {
			response["threat_analysis"] = threatAnalysis

			// 记录威胁检测指标
			if s.metrics != nil {
				s.metrics.RecordModuleAnalysis("ai")
				s.metrics.RecordThreatDetection(threatAnalysis.ThreatLevel == "high" || threatAnalysis.ThreatLevel == "critical")
				if threatAnalysis.RiskScore > 0 {
					s.metrics.RecordSecurityScore(100 - threatAnalysis.RiskScore)
				}
			}

			// 生成安全报告
			if includeReport {
				securityReport, err := s.aiAnalyzer.GenerateSecurityReport(ctx, threatAnalysis)
				if err != nil {
					response["report_generation_error"] = err.Error()
				} else {
					response["security_report"] = securityReport
				}
			}
		}
	}

	// 生成自然语言摘要
	response["summary"] = s.generateAnalysisSummary(result, response)

	return response, nil
}

// handleAnalyzeBatch 处理批量分析请求
func (s *MCPServer) handleAnalyzeBatch(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	targetsInterface := args["targets"].([]interface{})
	targets := make([]string, len(targetsInterface))
	for i, t := range targetsInterface {
		targets[i] = t.(string)
	}

	enableAI := getBoolArg(args, "enable_ai_analysis", false)
	depth := getStringArg(args, "analysis_depth", "basic")

	// 限制批量大小
	if len(targets) > 10 {
		return nil, fmt.Errorf("批量分析最多支持10个目标，当前: %d", len(targets))
	}

	// 记录到会话上下文
	session.Context["last_batch_targets"] = targets
	session.Context["last_batch_time"] = time.Now()

	// 配置分析器
	analyzer := s.configureAnalyzerByDepth(depth)

	// 执行批量分析
	start := time.Now()
	results, err := analyzer.BatchAnalyze(ctx, targets)
	duration := time.Since(start)

	if err != nil {
		return nil, fmt.Errorf("批量分析失败: %w", err)
	}

	// 统计结果
	successCount := 0
	var threatAnalyses []interface{}

	for _, result := range results {
		if s.metrics != nil {
			s.metrics.RecordRequest()
			if result.Success {
				s.metrics.RecordSuccess(time.Duration(result.AnalysisTime) * time.Millisecond)
				successCount++
			} else {
				s.metrics.RecordFailure()
			}
		}

		// AI分析（仅对成功的结果）
		if enableAI && s.aiAnalyzer != nil && result.Success {
			threatAnalysis, err := s.aiAnalyzer.AnalyzeWithAI(ctx, result)
			if err == nil {
				threatAnalyses = append(threatAnalyses, map[string]interface{}{
					"target":   result.Domain,
					"analysis": threatAnalysis,
				})
			}
		}
	}

	response := map[string]interface{}{
		"targets":           targets,
		"results":           results,
		"total_count":       len(targets),
		"success_count":     successCount,
		"failure_count":     len(targets) - successCount,
		"success_rate":      float64(successCount) / float64(len(targets)) * 100,
		"total_duration_ms": duration.Milliseconds(),
		"analysis_depth":    depth,
		"timestamp":         time.Now(),
	}

	if len(threatAnalyses) > 0 {
		response["threat_analyses"] = threatAnalyses
	}

	// 生成批量分析摘要
	response["summary"] = s.generateBatchSummary(targets, successCount, duration)

	return response, nil
}

// handleThreatAnalysis 处理威胁分析请求
func (s *MCPServer) handleThreatAnalysis(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	target := args["target"].(string)
	includeRecommendations := getBoolArg(args, "include_recommendations", true)
	threshold := getStringArg(args, "threat_level_threshold", "medium")

	if s.aiAnalyzer == nil {
		return nil, fmt.Errorf("AI分析器未启用")
	}

	// 首先进行网络分析
	result, err := s.analyzer.Analyze(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("网络分析失败: %w", err)
	}

	if !result.Success {
		return nil, fmt.Errorf("网络分析未成功，无法进行威胁分析")
	}

	// 执行威胁分析
	threatAnalysis, err := s.aiAnalyzer.AnalyzeWithAI(ctx, result)
	if err != nil {
		return nil, fmt.Errorf("威胁分析失败: %w", err)
	}

	// 记录指标
	if s.metrics != nil {
		s.metrics.RecordModuleAnalysis("ai")
		s.metrics.RecordThreatDetection(threatAnalysis.ThreatLevel == "high" || threatAnalysis.ThreatLevel == "critical")
	}

	response := map[string]interface{}{
		"target":          target,
		"threat_analysis": threatAnalysis,
		"threshold":       threshold,
		"meets_threshold": s.meetsThreshold(threatAnalysis.ThreatLevel, threshold),
		"timestamp":       time.Now(),
	}

	// 生成安全建议
	if includeRecommendations {
		recommendations := s.generateSecurityRecommendations(threatAnalysis, result)
		response["recommendations"] = recommendations
	}

	// 生成威胁分析摘要
	response["summary"] = s.generateThreatSummary(threatAnalysis)

	return response, nil
}

// handleGenerateSecurityReport 处理安全报告生成请求
func (s *MCPServer) handleGenerateSecurityReport(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	target := args["target"].(string)
	format := getStringArg(args, "report_format", "detailed")
	language := getStringArg(args, "language", "zh-CN")

	if s.aiAnalyzer == nil {
		return nil, fmt.Errorf("AI分析器未启用")
	}

	// 执行完整分析
	result, err := s.analyzer.Analyze(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("网络分析失败: %w", err)
	}

	threatAnalysis, err := s.aiAnalyzer.AnalyzeWithAI(ctx, result)
	if err != nil {
		return nil, fmt.Errorf("威胁分析失败: %w", err)
	}

	// 生成安全报告
	securityReport, err := s.aiAnalyzer.GenerateSecurityReport(ctx, threatAnalysis)
	if err != nil {
		return nil, fmt.Errorf("安全报告生成失败: %w", err)
	}

	// 根据格式调整报告内容
	formattedReport := s.formatSecurityReport(securityReport, format, language)

	response := map[string]interface{}{
		"target":          target,
		"security_report": formattedReport,
		"format":          format,
		"language":        language,
		"generated_at":    time.Now(),
	}

	return response, nil
}

// handleGetMetrics 处理指标查询请求
func (s *MCPServer) handleGetMetrics(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	metricType := getStringArg(args, "metric_type", "all")
	timeRange := getStringArg(args, "time_range", "24h")

	if s.metrics == nil {
		return nil, fmt.Errorf("指标收集未启用")
	}

	snapshot := s.metrics.GetSnapshot()

	response := map[string]interface{}{
		"metric_type": metricType,
		"time_range":  timeRange,
		"timestamp":   time.Now(),
	}

	switch metricType {
	case "performance":
		response["metrics"] = map[string]interface{}{
			"total_requests":      snapshot.TotalRequests,
			"successful_requests": snapshot.SuccessfulRequests,
			"failed_requests":     snapshot.FailedRequests,
			"success_rate":        snapshot.SuccessRate,
			"avg_response_time":   snapshot.AvgResponseTime,
			"concurrent_requests": snapshot.ConcurrentRequests,
			"max_concurrency":     snapshot.MaxConcurrency,
		}
	case "security":
		response["metrics"] = map[string]interface{}{
			"threats_detected":   snapshot.ThreatsDetected,
			"high_risk_domains":  snapshot.HighRiskDomains,
			"avg_security_score": snapshot.AvgSecurityScore,
		}
	case "usage":
		response["metrics"] = map[string]interface{}{
			"cache_hit_rate": snapshot.CacheHitRate,
			"cache_hits":     snapshot.CacheHits,
			"cache_misses":   snapshot.CacheMisses,
			"uptime":         snapshot.Uptime,
			"module_metrics": snapshot.ModuleMetrics,
			"module_errors":  snapshot.ModuleErrors,
		}
	default: // "all"
		response["metrics"] = snapshot
	}

	return response, nil
}

// handleManageSession 处理会话管理请求
func (s *MCPServer) handleManageSession(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	action := args["action"].(string)

	switch action {
	case "save_context":
		key := getStringArg(args, "key", "")
		if key == "" {
			return nil, fmt.Errorf("保存上下文需要指定key")
		}
		value := args["value"]
		session.Context[key] = value
		return map[string]interface{}{
			"action":  "save_context",
			"key":     key,
			"success": true,
		}, nil

	case "load_context":
		key := getStringArg(args, "key", "")
		if key == "" {
			return nil, fmt.Errorf("加载上下文需要指定key")
		}
		value, exists := session.Context[key]
		return map[string]interface{}{
			"action": "load_context",
			"key":    key,
			"value":  value,
			"exists": exists,
		}, nil

	case "clear_context":
		session.Context = make(map[string]interface{})
		return map[string]interface{}{
			"action":  "clear_context",
			"success": true,
		}, nil

	case "list_history":
		return map[string]interface{}{
			"action":        "list_history",
			"session_id":    session.ID,
			"created_at":    session.CreatedAt,
			"last_used":     session.LastUsed,
			"request_count": session.RequestCount,
			"context_keys":  getMapKeys(session.Context),
		}, nil

	default:
		return nil, fmt.Errorf("未知的会话操作: %s", action)
	}
}

// handleConfigureAnalysis 处理分析配置请求
func (s *MCPServer) handleConfigureAnalysis(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	// 将配置保存到会话上下文中
	config := make(map[string]interface{})

	if timeout, exists := args["timeout"]; exists {
		config["timeout"] = timeout
	}

	if modules, exists := args["enable_modules"]; exists {
		config["enable_modules"] = modules
	}

	if concurrency, exists := args["concurrency"]; exists {
		config["concurrency"] = concurrency
	}

	session.Context["analysis_config"] = config

	return map[string]interface{}{
		"action":  "configure_analysis",
		"config":  config,
		"success": true,
	}, nil
}

// handleGetHelp 处理帮助请求
func (s *MCPServer) handleGetHelp(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	topic := getStringArg(args, "topic", "overview")
	toolName := getStringArg(args, "tool_name", "")

	help := s.generateHelpContent(topic, toolName)

	return map[string]interface{}{
		"topic":   topic,
		"content": help,
	}, nil
}

// 辅助函数
func getBoolArg(args map[string]interface{}, key string, defaultValue bool) bool {
	if value, exists := args[key]; exists {
		if boolValue, ok := value.(bool); ok {
			return boolValue
		}
	}
	return defaultValue
}

func getStringArg(args map[string]interface{}, key string, defaultValue string) string {
	if value, exists := args[key]; exists {
		if stringValue, ok := value.(string); ok {
			return stringValue
		}
	}
	return defaultValue
}

func getIntArg(args map[string]interface{}, key string, defaultValue int) int {
	if value, exists := args[key]; exists {
		switch v := value.(type) {
		case int:
			return v
		case float64:
			return int(v)
		}
	}
	return defaultValue
}

// configureAnalyzerByDepth 根据分析深度配置分析器
func (s *MCPServer) configureAnalyzerByDepth(depth string) *network.Analyzer {
	switch depth {
	case "basic":
		return network.NewAnalyzer(
			network.WithGeoLocation(false),
			network.WithTLSSecurity(false),
			network.WithFingerprint(false),
			network.WithWHOISLookup(true),
			network.WithCDNDetection(false),
			network.WithTimeout(15*time.Second),
		)
	case "full":
		return network.NewAnalyzer(
			network.WithGeoLocation(true),
			network.WithTLSSecurity(true),
			network.WithFingerprint(true),
			network.WithWHOISLookup(true),
			network.WithCDNDetection(true),
			network.WithTimeout(60*time.Second),
		)
	default: // "standard"
		return s.analyzer
	}
}

// generateAnalysisSummary 生成分析摘要
func (s *MCPServer) generateAnalysisSummary(result *schema.NetworkResult, response map[string]interface{}) string {
	if !result.Success {
		return fmt.Sprintf("❌ 对 %s 的分析失败", result.Domain)
	}

	summary := fmt.Sprintf("✅ 成功分析 %s (IP: %s)", result.Domain, result.IP)

	if threatAnalysis, exists := response["threat_analysis"]; exists {
		if threat, ok := threatAnalysis.(*network.ThreatAnalysis); ok {
			summary += fmt.Sprintf("\n🛡️ 威胁等级: %s, 风险评分: %d", threat.ThreatLevel, threat.RiskScore)
		}
	}

	if result.Advanced != nil {
		if result.Advanced.GeoLocation != nil {
			geo := result.Advanced.GeoLocation
			summary += fmt.Sprintf("\n🌍 位置: %s, %s", geo.Country, geo.City)
		}

		if result.Advanced.TLSSecurity != nil {
			tls := result.Advanced.TLSSecurity
			summary += fmt.Sprintf("\n🔒 TLS安全: %s", tls.SecurityRating)
		}
	}

	return summary
}

// generateBatchSummary 生成批量分析摘要
func (s *MCPServer) generateBatchSummary(targets []string, successCount int, duration time.Duration) string {
	return fmt.Sprintf("📊 批量分析完成: %d/%d 成功 (%.1f%%), 耗时: %v",
		successCount, len(targets),
		float64(successCount)/float64(len(targets))*100,
		duration)
}

// generateThreatSummary 生成威胁分析摘要
func (s *MCPServer) generateThreatSummary(threat *network.ThreatAnalysis) string {
	emoji := map[string]string{
		"low":      "🟢",
		"medium":   "🟡",
		"high":     "🟠",
		"critical": "🔴",
	}

	return fmt.Sprintf("%s 威胁等级: %s, 风险评分: %d, 置信度: %.2f",
		emoji[threat.ThreatLevel], threat.ThreatLevel, threat.RiskScore, threat.Confidence)
}

// meetsThreshold 检查是否满足威胁等级阈值
func (s *MCPServer) meetsThreshold(level, threshold string) bool {
	levels := map[string]int{
		"low":      1,
		"medium":   2,
		"high":     3,
		"critical": 4,
	}

	return levels[level] >= levels[threshold]
}

// generateSecurityRecommendations 生成安全建议
func (s *MCPServer) generateSecurityRecommendations(threat *network.ThreatAnalysis, result *schema.NetworkResult) []map[string]interface{} {
	recommendations := make([]map[string]interface{}, 0)

	// 基于威胁等级的建议
	if threat.ThreatLevel == "high" || threat.ThreatLevel == "critical" {
		recommendations = append(recommendations, map[string]interface{}{
			"priority":    "high",
			"category":    "威胁防护",
			"title":       "立即加强安全监控",
			"description": "检测到高风险威胁，建议立即加强对该目标的安全监控",
		})
	}

	// 基于TLS安全的建议
	if result.Advanced != nil && result.Advanced.TLSSecurity != nil {
		tls := result.Advanced.TLSSecurity
		if tls.SecurityRating == "F" || tls.SecurityRating == "D" {
			recommendations = append(recommendations, map[string]interface{}{
				"priority":    "high",
				"category":    "TLS安全",
				"title":       "升级TLS配置",
				"description": "当前TLS配置存在安全风险，建议升级到TLS 1.3并使用强密码套件",
			})
		}
	}

	// 添加通用建议
	for _, rec := range threat.Recommendations {
		recommendations = append(recommendations, map[string]interface{}{
			"priority":    "medium",
			"category":    "通用建议",
			"title":       rec,
			"description": rec,
		})
	}

	return recommendations
}

// formatSecurityReport 格式化安全报告
func (s *MCPServer) formatSecurityReport(report *network.SecurityReport, format, language string) interface{} {
	switch format {
	case "summary":
		return map[string]interface{}{
			"summary":        report.Summary,
			"security_score": report.SecurityScore,
			"threat_level":   report.ThreatAnalysis.ThreatLevel,
			"key_findings":   report.ThreatAnalysis.ThreatTypes,
		}
	case "executive":
		return map[string]interface{}{
			"executive_summary": report.Summary,
			"risk_assessment": map[string]interface{}{
				"overall_risk":   report.ThreatAnalysis.ThreatLevel,
				"risk_score":     report.ThreatAnalysis.RiskScore,
				"security_score": report.SecurityScore,
			},
			"key_recommendations": report.Recommendations[:min(3, len(report.Recommendations))],
		}
	default: // "detailed"
		return report
	}
}

// generateHelpContent 生成帮助内容
func (s *MCPServer) generateHelpContent(topic, toolName string) string {
	if toolName != "" {
		if tool, exists := s.tools[toolName]; exists {
			return fmt.Sprintf("工具: %s\n描述: %s\n\n参数说明:\n%v",
				tool.Name, tool.Description, tool.InputSchema)
		}
		return fmt.Sprintf("未找到工具: %s", toolName)
	}

	switch topic {
	case "overview":
		return `Eino网络分析MCP服务器

这是一个基于MCP协议的网络安全分析服务，提供以下功能：
• 域名和IP地址分析
• DNS、WHOIS、地理位置查询
• TLS安全评估和网站指纹识别
• AI驱动的威胁分析和安全报告
• 批量分析和性能监控

使用 'get_help' 工具获取更多帮助信息。`

	case "tools":
		toolList := "可用工具列表:\n"
		for name, tool := range s.tools {
			toolList += fmt.Sprintf("• %s: %s\n", name, tool.Description)
		}
		return toolList

	case "examples":
		return `使用示例:

1. 分析单个域名:
   工具: analyze_domain
   参数: {"target": "example.com", "enable_ai_analysis": true}

2. 批量分析:
   工具: analyze_batch
   参数: {"targets": ["example.com", "google.com"]}

3. 威胁分析:
   工具: threat_analysis
   参数: {"target": "example.com", "include_recommendations": true}`

	case "troubleshooting":
		return `常见问题解决:

1. 分析超时: 尝试使用 'basic' 分析深度
2. AI分析失败: 检查目标是否可访问
3. 批量分析限制: 单次最多10个目标
4. 速率限制: 每分钟最多60次请求

如需更多帮助，请查看工具的详细参数说明。`

	default:
		return "未知的帮助主题"
	}
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
