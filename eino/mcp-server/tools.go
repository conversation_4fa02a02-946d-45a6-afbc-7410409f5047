/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package main

import (
	"context"
	"fmt"
)

// MCPTool MCP工具接口
type MCPTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
	Handler     ToolHandler            `json:"-"`
}

// MCPToolInfo MCP工具信息
type MCPToolInfo struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// ToolHandler 工具处理器函数类型
type ToolHandler func(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error)

// registerTools 注册所有工具
func (s *MCPServer) registerTools() {
	// 1. 域名分析工具
	s.registerTool(&MCPTool{
		Name:        "analyze_domain",
		Description: "分析单个域名或IP地址，获取DNS、WHOIS、地理位置、TLS安全、技术栈等信息",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"target": map[string]interface{}{
					"type":        "string",
					"description": "要分析的域名或IP地址",
				},
				"enable_ai_analysis": map[string]interface{}{
					"type":        "boolean",
					"description": "是否启用AI威胁分析",
					"default":     true,
				},
				"include_security_report": map[string]interface{}{
					"type":        "boolean",
					"description": "是否包含安全报告",
					"default":     false,
				},
				"analysis_depth": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"basic", "standard", "full"},
					"description": "分析深度级别",
					"default":     "standard",
				},
			},
			"required": []string{"target"},
		},
		Handler: s.handleAnalyzeDomain,
	})

	// 2. 批量分析工具
	s.registerTool(&MCPTool{
		Name:        "analyze_batch",
		Description: "批量分析多个域名或IP地址，支持并发处理",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"targets": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "要分析的域名或IP地址列表",
					"maxItems":    10, // 限制批量大小
				},
				"enable_ai_analysis": map[string]interface{}{
					"type":        "boolean",
					"description": "是否启用AI威胁分析",
					"default":     false,
				},
				"analysis_depth": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"basic", "standard"},
					"description": "分析深度级别",
					"default":     "basic",
				},
			},
			"required": []string{"targets"},
		},
		Handler: s.handleAnalyzeBatch,
	})

	// 3. AI威胁分析工具
	s.registerTool(&MCPTool{
		Name:        "threat_analysis",
		Description: "对已分析的网络数据进行AI威胁评估和安全分析",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"target": map[string]interface{}{
					"type":        "string",
					"description": "要进行威胁分析的域名或IP",
				},
				"include_recommendations": map[string]interface{}{
					"type":        "boolean",
					"description": "是否包含安全建议",
					"default":     true,
				},
				"threat_level_threshold": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"low", "medium", "high", "critical"},
					"description": "威胁等级阈值",
					"default":     "medium",
				},
			},
			"required": []string{"target"},
		},
		Handler: s.handleThreatAnalysis,
	})

	// 4. 安全报告生成工具
	s.registerTool(&MCPTool{
		Name:        "generate_security_report",
		Description: "生成详细的安全分析报告，包含威胁评估和安全建议",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"target": map[string]interface{}{
					"type":        "string",
					"description": "要生成报告的域名或IP",
				},
				"report_format": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"summary", "detailed", "executive"},
					"description": "报告格式类型",
					"default":     "detailed",
				},
				"language": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"zh-CN", "en-US"},
					"description": "报告语言",
					"default":     "zh-CN",
				},
			},
			"required": []string{"target"},
		},
		Handler: s.handleGenerateSecurityReport,
	})

	// 5. 指标查询工具
	s.registerTool(&MCPTool{
		Name:        "get_metrics",
		Description: "获取服务器性能指标和分析统计信息",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"metric_type": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"performance", "security", "usage", "all"},
					"description": "指标类型",
					"default":     "all",
				},
				"time_range": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"1h", "24h", "7d", "30d"},
					"description": "时间范围",
					"default":     "24h",
				},
			},
		},
		Handler: s.handleGetMetrics,
	})

	// 6. 会话管理工具
	s.registerTool(&MCPTool{
		Name:        "manage_session",
		Description: "管理分析会话，保存和检索分析上下文",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"action": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"save_context", "load_context", "clear_context", "list_history"},
					"description": "会话操作类型",
				},
				"key": map[string]interface{}{
					"type":        "string",
					"description": "上下文键名（用于保存/加载）",
				},
				"value": map[string]interface{}{
					"description": "要保存的上下文值",
				},
			},
			"required": []string{"action"},
		},
		Handler: s.handleManageSession,
	})

	// 7. 配置管理工具
	s.registerTool(&MCPTool{
		Name:        "configure_analysis",
		Description: "配置分析参数和选项，自定义分析行为",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"timeout": map[string]interface{}{
					"type":        "integer",
					"description": "分析超时时间（秒）",
					"minimum":     5,
					"maximum":     300,
				},
				"enable_modules": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
						"enum": []string{"dns", "whois", "geo", "tls", "fingerprint", "cdn"},
					},
					"description": "启用的分析模块",
				},
				"concurrency": map[string]interface{}{
					"type":        "integer",
					"description": "并发数",
					"minimum":     1,
					"maximum":     10,
				},
			},
		},
		Handler: s.handleConfigureAnalysis,
	})

	// 8. 帮助和文档工具
	s.registerTool(&MCPTool{
		Name:        "get_help",
		Description: "获取工具使用帮助和文档信息",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"topic": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"overview", "tools", "examples", "troubleshooting"},
					"description": "帮助主题",
					"default":     "overview",
				},
				"tool_name": map[string]interface{}{
					"type":        "string",
					"description": "特定工具的帮助（可选）",
				},
			},
		},
		Handler: s.handleGetHelp,
	})
}

// registerTool 注册单个工具
func (s *MCPServer) registerTool(tool *MCPTool) {
	s.tools[tool.Name] = tool
}

// Execute 执行工具
func (t *MCPTool) Execute(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	// 参数验证
	if err := t.validateArguments(args); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 执行工具处理器
	return t.Handler(ctx, args, session, server)
}

// validateArguments 验证工具参数
func (t *MCPTool) validateArguments(args map[string]interface{}) error {
	// 这里应该实现JSON Schema验证
	// 为了简化，我们只做基本的必需参数检查
	
	schema := t.InputSchema
	properties, ok := schema["properties"].(map[string]interface{})
	if !ok {
		return nil
	}

	required, ok := schema["required"].([]string)
	if !ok {
		return nil
	}

	// 检查必需参数
	for _, field := range required {
		if _, exists := args[field]; !exists {
			return fmt.Errorf("缺少必需参数: %s", field)
		}
	}

	// 检查参数类型（简化版本）
	for field, value := range args {
		if propSchema, exists := properties[field]; exists {
			if err := validateFieldType(field, value, propSchema.(map[string]interface{})); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateFieldType 验证字段类型
func validateFieldType(field string, value interface{}, schema map[string]interface{}) error {
	expectedType, ok := schema["type"].(string)
	if !ok {
		return nil
	}

	switch expectedType {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("参数 %s 应为字符串类型", field)
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("参数 %s 应为布尔类型", field)
		}
	case "integer":
		switch v := value.(type) {
		case int, int32, int64, float64:
			// 检查范围
			if min, exists := schema["minimum"]; exists {
				if minVal, ok := min.(float64); ok {
					if getNumericValue(v) < minVal {
						return fmt.Errorf("参数 %s 值过小，最小值为 %v", field, minVal)
					}
				}
			}
			if max, exists := schema["maximum"]; exists {
				if maxVal, ok := max.(float64); ok {
					if getNumericValue(v) > maxVal {
						return fmt.Errorf("参数 %s 值过大，最大值为 %v", field, maxVal)
					}
				}
			}
		default:
			return fmt.Errorf("参数 %s 应为数字类型", field)
		}
	case "array":
		if arr, ok := value.([]interface{}); ok {
			// 检查数组长度
			if maxItems, exists := schema["maxItems"]; exists {
				if maxVal, ok := maxItems.(float64); ok {
					if len(arr) > int(maxVal) {
						return fmt.Errorf("参数 %s 数组长度超出限制，最大长度为 %v", field, maxVal)
					}
				}
			}
		} else {
			return fmt.Errorf("参数 %s 应为数组类型", field)
		}
	}

	// 检查枚举值
	if enum, exists := schema["enum"]; exists {
		if enumValues, ok := enum.([]string); ok {
			valueStr, ok := value.(string)
			if !ok {
				return fmt.Errorf("参数 %s 应为字符串类型以匹配枚举值", field)
			}
			
			found := false
			for _, enumValue := range enumValues {
				if valueStr == enumValue {
					found = true
					break
				}
			}
			if !found {
				return fmt.Errorf("参数 %s 值 '%s' 不在允许的枚举值中: %v", field, valueStr, enumValues)
			}
		}
	}

	return nil
}

// getNumericValue 获取数值
func getNumericValue(value interface{}) float64 {
	switch v := value.(type) {
	case int:
		return float64(v)
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case float64:
		return v
	case float32:
		return float64(v)
	default:
		return 0
	}
}
