/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/cloudwego/eino/components/network"
)

// MCPServer MCP服务器
type MCPServer struct {
	analyzer   *network.Analyzer
	aiAnalyzer *network.AIAnalyzer
	metrics    *network.MetricsCollector
	tools      map[string]*MCPTool
	config     *MCPConfig
	sessions   map[string]*Session
}

// MCPConfig MCP服务器配置
type MCPConfig struct {
	ServerName    string        `json:"server_name"`
	Version       string        `json:"version"`
	MaxSessions   int           `json:"max_sessions"`
	SessionTTL    time.Duration `json:"session_ttl"`
	EnableMetrics bool          `json:"enable_metrics"`
	EnableAI      bool          `json:"enable_ai"`
	RateLimit     int           `json:"rate_limit"` // 每分钟请求限制
}

// Session 会话管理
type Session struct {
	ID           string                 `json:"id"`
	CreatedAt    time.Time              `json:"created_at"`
	LastUsed     time.Time              `json:"last_used"`
	RequestCount int                    `json:"request_count"`
	Context      map[string]interface{} `json:"context"`
}

// MCPRequest MCP请求格式
type MCPRequest struct {
	Method    string                 `json:"method"`
	Params    map[string]interface{} `json:"params,omitempty"`
	ID        interface{}            `json:"id,omitempty"`
	SessionID string                 `json:"session_id,omitempty"`
}

// MCPResponse MCP响应格式
type MCPResponse struct {
	Result interface{} `json:"result,omitempty"`
	Error  *MCPError   `json:"error,omitempty"`
	ID     interface{} `json:"id,omitempty"`
}

// MCPError MCP错误格式
type MCPError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// DefaultMCPConfig 返回默认MCP配置
func DefaultMCPConfig() *MCPConfig {
	return &MCPConfig{
		ServerName:    "Eino Network Analysis MCP Server",
		Version:       "1.0.0",
		MaxSessions:   100,
		SessionTTL:    24 * time.Hour,
		EnableMetrics: true,
		EnableAI:      true,
		RateLimit:     60, // 每分钟60次请求
	}
}

// NewMCPServer 创建新的MCP服务器
func NewMCPServer(config *MCPConfig) (*MCPServer, error) {
	if config == nil {
		config = DefaultMCPConfig()
	}

	// 创建网络分析器
	analyzer := network.NewAnalyzer(
		network.WithGeoLocation(true),
		network.WithTLSSecurity(true),
		network.WithFingerprint(true),
		network.WithWHOISLookup(true),
		network.WithCDNDetection(true),
		network.WithTimeout(30*time.Second),
		network.WithConcurrency(5, 10),
	)

	// 创建AI分析器
	var aiAnalyzer *network.AIAnalyzer
	if config.EnableAI {
		securityModel := network.NewMockSecurityModel()
		aiAnalyzer = network.NewAIAnalyzer(network.DefaultAIConfig(), securityModel)
	}

	// 创建指标收集器
	var metrics *network.MetricsCollector
	if config.EnableMetrics {
		metrics = network.NewMetricsCollector(network.DefaultMetricsConfig())
	}

	server := &MCPServer{
		analyzer:   analyzer,
		aiAnalyzer: aiAnalyzer,
		metrics:    metrics,
		tools:      make(map[string]*MCPTool),
		config:     config,
		sessions:   make(map[string]*Session),
	}

	// 注册工具
	server.registerTools()

	return server, nil
}

// Start 启动MCP服务器
func (s *MCPServer) Start(ctx context.Context) error {
	log.Printf("启动MCP服务器: %s v%s", s.config.ServerName, s.config.Version)
	log.Printf("已注册 %d 个工具", len(s.tools))

	// 启动会话清理协程
	go s.sessionCleanup(ctx)

	// 处理MCP请求的主循环
	return s.handleRequests(ctx)
}

// handleRequests 处理MCP请求
func (s *MCPServer) handleRequests(ctx context.Context) error {
	// 这里应该实现具体的MCP协议处理
	// 为了演示，我们创建一个简单的JSON-RPC处理器

	log.Println("MCP服务器已启动，等待请求...")

	// 模拟处理请求
	select {
	case <-ctx.Done():
		log.Println("MCP服务器正在关闭...")
		return ctx.Err()
	}
}

// HandleRequest 处理单个MCP请求
func (s *MCPServer) HandleRequest(ctx context.Context, request *MCPRequest) *MCPResponse {
	// 记录请求指标
	if s.metrics != nil {
		s.metrics.RecordRequest()
	}

	start := time.Now()
	defer func() {
		duration := time.Since(start)
		log.Printf("处理请求 %s 耗时: %v", request.Method, duration)
	}()

	// 会话管理
	session := s.getOrCreateSession(request.SessionID)
	session.LastUsed = time.Now()
	session.RequestCount++

	// 速率限制检查
	if err := s.checkRateLimit(session); err != nil {
		return &MCPResponse{
			Error: &MCPError{
				Code:    429,
				Message: "请求频率过高，请稍后重试",
				Data:    map[string]interface{}{"retry_after": 60},
			},
			ID: request.ID,
		}
	}

	// 路由到具体的工具处理器
	switch request.Method {
	case "tools/list":
		return s.handleListTools(request)
	case "tools/call":
		return s.handleToolCall(ctx, request, session)
	case "server/info":
		return s.handleServerInfo(request)
	case "session/info":
		return s.handleSessionInfo(request, session)
	default:
		return &MCPResponse{
			Error: &MCPError{
				Code:    -32601,
				Message: fmt.Sprintf("未知方法: %s", request.Method),
			},
			ID: request.ID,
		}
	}
}

// handleListTools 处理工具列表请求
func (s *MCPServer) handleListTools(request *MCPRequest) *MCPResponse {
	tools := make([]*MCPToolInfo, 0, len(s.tools))
	for _, tool := range s.tools {
		tools = append(tools, &MCPToolInfo{
			Name:        tool.Name,
			Description: tool.Description,
			InputSchema: tool.InputSchema,
		})
	}

	return &MCPResponse{
		Result: map[string]interface{}{
			"tools": tools,
		},
		ID: request.ID,
	}
}

// handleToolCall 处理工具调用请求
func (s *MCPServer) handleToolCall(ctx context.Context, request *MCPRequest, session *Session) *MCPResponse {
	// 解析工具调用参数
	toolName, ok := request.Params["name"].(string)
	if !ok {
		return &MCPResponse{
			Error: &MCPError{
				Code:    -32602,
				Message: "缺少工具名称参数",
			},
			ID: request.ID,
		}
	}

	arguments, ok := request.Params["arguments"].(map[string]interface{})
	if !ok {
		arguments = make(map[string]interface{})
	}

	// 查找工具
	tool, exists := s.tools[toolName]
	if !exists {
		return &MCPResponse{
			Error: &MCPError{
				Code:    -32602,
				Message: fmt.Sprintf("未知工具: %s", toolName),
			},
			ID: request.ID,
		}
	}

	// 执行工具
	result, err := tool.Execute(ctx, arguments, session, s)
	if err != nil {
		if s.metrics != nil {
			s.metrics.RecordFailure()
		}
		return &MCPResponse{
			Error: &MCPError{
				Code:    -32603,
				Message: fmt.Sprintf("工具执行失败: %v", err),
			},
			ID: request.ID,
		}
	}

	if s.metrics != nil {
		s.metrics.RecordSuccess(time.Since(time.Now()))
	}

	return &MCPResponse{
		Result: result,
		ID:     request.ID,
	}
}

// handleServerInfo 处理服务器信息请求
func (s *MCPServer) handleServerInfo(request *MCPRequest) *MCPResponse {
	info := map[string]interface{}{
		"name":         s.config.ServerName,
		"version":      s.config.Version,
		"capabilities": []string{"tools", "sessions", "metrics"},
		"tools_count":  len(s.tools),
		"sessions":     len(s.sessions),
	}

	if s.metrics != nil {
		snapshot := s.metrics.GetSnapshot()
		info["metrics"] = map[string]interface{}{
			"total_requests":    snapshot.TotalRequests,
			"success_rate":      snapshot.SuccessRate,
			"avg_response_time": snapshot.AvgResponseTime,
			"uptime_seconds":    snapshot.Uptime,
		}
	}

	return &MCPResponse{
		Result: info,
		ID:     request.ID,
	}
}

// handleSessionInfo 处理会话信息请求
func (s *MCPServer) handleSessionInfo(request *MCPRequest, session *Session) *MCPResponse {
	return &MCPResponse{
		Result: map[string]interface{}{
			"session_id":    session.ID,
			"created_at":    session.CreatedAt,
			"last_used":     session.LastUsed,
			"request_count": session.RequestCount,
			"context_keys":  getMapKeys(session.Context),
		},
		ID: request.ID,
	}
}

// getOrCreateSession 获取或创建会话
func (s *MCPServer) getOrCreateSession(sessionID string) *Session {
	if sessionID == "" {
		sessionID = generateSessionID()
	}

	session, exists := s.sessions[sessionID]
	if !exists {
		session = &Session{
			ID:        sessionID,
			CreatedAt: time.Now(),
			LastUsed:  time.Now(),
			Context:   make(map[string]interface{}),
		}
		s.sessions[sessionID] = session
	}

	return session
}

// checkRateLimit 检查速率限制
func (s *MCPServer) checkRateLimit(session *Session) error {
	// 简单的速率限制实现
	if session.RequestCount > s.config.RateLimit {
		// 检查是否在同一分钟内
		if time.Since(session.CreatedAt) < time.Minute {
			return fmt.Errorf("速率限制超出")
		}
		// 重置计数器
		session.RequestCount = 0
		session.CreatedAt = time.Now()
	}
	return nil
}

// sessionCleanup 会话清理协程
func (s *MCPServer) sessionCleanup(ctx context.Context) {
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			now := time.Now()
			for id, session := range s.sessions {
				if now.Sub(session.LastUsed) > s.config.SessionTTL {
					delete(s.sessions, id)
					log.Printf("清理过期会话: %s", id)
				}
			}
		}
	}
}

// 辅助函数
func generateSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}

func getMapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func main() {
	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 处理中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigChan
		log.Println("收到中断信号，正在关闭服务器...")
		cancel()
	}()

	// 创建并启动MCP服务器
	server, err := NewMCPServer(DefaultMCPConfig())
	if err != nil {
		log.Fatalf("创建MCP服务器失败: %v", err)
	}

	if err := server.Start(ctx); err != nil && err != context.Canceled {
		log.Fatalf("MCP服务器运行失败: %v", err)
	}

	log.Println("MCP服务器已关闭")
}
