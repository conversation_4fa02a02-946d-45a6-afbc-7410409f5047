# Eino网络分析MCP服务器

这是一个基于MCP (Model Context Protocol) 协议的网络安全分析服务器，为大语言模型提供专业的网络分析能力。

## 🚀 功能特性

### 核心分析工具
- **analyze_domain** - 单域名/IP分析，支持DNS、WHOIS、地理位置、TLS安全、技术栈检测
- **analyze_batch** - 批量分析，支持并发处理多个目标
- **threat_analysis** - AI驱动的威胁分析和风险评估
- **generate_security_report** - 生成详细的安全分析报告
- **get_metrics** - 获取服务器性能指标和分析统计
- **manage_session** - 会话管理和上下文保存
- **configure_analysis** - 动态配置分析参数
- **get_help** - 获取工具使用帮助

### 高级特性
- 🤖 **AI威胁分析** - 智能安全评估和威胁检测
- 📊 **性能监控** - 完整的指标收集和统计
- 💾 **会话管理** - 保存分析上下文和历史记录
- 🔧 **动态配置** - 运行时调整分析参数
- 🌍 **中文支持** - 完整的中文界面和报告
- 🛡️ **安全防护** - 速率限制和参数验证
- 📝 **自然语言摘要** - 为大模型优化的结果描述

## 📦 安装和运行

### 1. 编译服务器
```bash
cd eino/mcp-server
go build -o mcp-server .
```

### 2. 运行服务器
```bash
./mcp-server
```

### 3. 运行测试
```bash
go run test_client.go
```

## 🔧 配置选项

### 服务器配置
```go
type MCPConfig struct {
    ServerName    string        // 服务器名称
    Version       string        // 版本号
    MaxSessions   int           // 最大会话数
    SessionTTL    time.Duration // 会话生存时间
    EnableMetrics bool          // 启用指标收集
    EnableAI      bool          // 启用AI分析
    RateLimit     int           // 速率限制（每分钟）
}
```

### 分析深度级别
- **basic** - 基础分析（DNS + WHOIS）
- **standard** - 标准分析（包含地理位置和TLS）
- **full** - 完整分析（所有模块）

## 🛠️ MCP工具详解

### 1. analyze_domain
分析单个域名或IP地址

**参数：**
```json
{
  "target": "example.com",           // 必需：目标域名或IP
  "enable_ai_analysis": true,        // 可选：启用AI分析
  "include_security_report": false,  // 可选：包含安全报告
  "analysis_depth": "standard"       // 可选：分析深度
}
```

**响应：**
```json
{
  "target": "example.com",
  "analysis_result": { /* 详细分析结果 */ },
  "threat_analysis": { /* AI威胁分析 */ },
  "summary": "✅ 成功分析 example.com (IP: *************)..."
}
```

### 2. analyze_batch
批量分析多个目标

**参数：**
```json
{
  "targets": ["example.com", "google.com"],  // 必需：目标列表（最多10个）
  "enable_ai_analysis": false,               // 可选：启用AI分析
  "analysis_depth": "basic"                  // 可选：分析深度
}
```

### 3. threat_analysis
AI威胁分析

**参数：**
```json
{
  "target": "example.com",                    // 必需：分析目标
  "include_recommendations": true,            // 可选：包含安全建议
  "threat_level_threshold": "medium"          // 可选：威胁等级阈值
}
```

### 4. get_metrics
获取性能指标

**参数：**
```json
{
  "metric_type": "all",     // 可选：指标类型 (performance/security/usage/all)
  "time_range": "24h"       // 可选：时间范围 (1h/24h/7d/30d)
}
```

## 🔗 与大模型集成

### 1. 工具调用示例
大模型可以通过以下方式调用网络分析功能：

```json
{
  "method": "tools/call",
  "params": {
    "name": "analyze_domain",
    "arguments": {
      "target": "suspicious-site.com",
      "enable_ai_analysis": true,
      "include_security_report": true
    }
  }
}
```

### 2. 自然语言交互
服务器为每个分析结果生成自然语言摘要，便于大模型理解和向用户解释：

```
✅ 成功分析 example.com (IP: *************)
🛡️ 威胁等级: low, 风险评分: 20
🌍 位置: 美国, 弗吉尼亚州
🔒 TLS安全: A
```

### 3. 上下文管理
支持会话上下文，大模型可以：
- 保存分析历史
- 关联多次查询
- 维护用户偏好

## 📊 性能监控

### 指标类型
- **性能指标**: 请求数、成功率、响应时间、并发数
- **安全指标**: 威胁检测数、高风险域名数、平均安全评分
- **使用指标**: 缓存命中率、模块使用统计、运行时间

### 监控面板
```bash
# 获取实时指标
curl -X POST http://localhost:8080/mcp \
  -d '{"method":"tools/call","params":{"name":"get_metrics","arguments":{"metric_type":"all"}}}'
```

## 🛡️ 安全特性

### 1. 速率限制
- 每个会话每分钟最多60次请求
- 自动重置计数器
- 优雅的错误响应

### 2. 参数验证
- JSON Schema验证
- 类型检查和范围验证
- 枚举值验证

### 3. 会话管理
- 自动会话清理
- 上下文隔离
- 内存使用控制

## 🔍 故障排除

### 常见问题

1. **分析超时**
   - 解决方案：使用 `basic` 分析深度
   - 检查网络连接

2. **AI分析失败**
   - 确保目标可访问
   - 检查AI分析器配置

3. **批量分析限制**
   - 单次最多10个目标
   - 考虑分批处理

4. **速率限制**
   - 每分钟最多60次请求
   - 实现客户端重试机制

### 日志级别
- `INFO`: 正常操作日志
- `WARN`: 警告信息
- `ERROR`: 错误信息
- `DEBUG`: 调试信息

## 🚀 扩展开发

### 添加新工具
1. 在 `tools.go` 中定义工具
2. 在 `handlers.go` 中实现处理器
3. 注册到服务器

### 自定义AI模型
实现 `SecurityModel` 接口：
```go
type SecurityModel interface {
    AnalyzeThreat(ctx context.Context, data *schema.NetworkResult) (*ThreatAnalysis, error)
    GenerateReport(ctx context.Context, analysis *ThreatAnalysis) (*SecurityReport, error)
    ScoreRisk(ctx context.Context, data *schema.NetworkResult) (int, error)
}
```

## 📄 许可证

Apache License 2.0 - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 查看文档和示例
- 使用 `get_help` 工具获取帮助
