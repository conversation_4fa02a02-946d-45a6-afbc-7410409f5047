package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/cloudwego/eino/components/network"
)

func main() {
	fmt.Println("=== 测试Eino网络分析组件的基础功能 ===\n")

	ctx := context.Background()

	// 1. 测试基础网络分析器
	fmt.Println("🔍 1. 测试基础网络分析功能...")
	analyzer := network.NewAnalyzer(
		network.WithGeoLocation(true),
		network.WithTLSSecurity(true),
		network.WithFingerprint(true),
		network.WithWHOISLookup(true),
		network.WithCDNDetection(true),
		network.WithTimeout(30*time.Second),
	)

	// 测试单个域名分析
	testDomains := []string{"www.baidu.com", "***************", "************"}

	for i, domain := range testDomains {
		fmt.Printf("\n--- 测试 %d: %s ---\n", i+1, domain)
		start := time.Now()

		result, err := analyzer.Analyze(ctx, domain)
		duration := time.Since(start)

		if err != nil {
			log.Printf("❌ 分析失败: %v", err)
			continue
		}

		fmt.Printf("✅ 基础分析成功 (耗时: %v)\n", duration)
		fmt.Printf("   域名: %s\n", result.Domain)
		fmt.Printf("   IP地址: %s\n", result.IP)
		fmt.Printf("   分析耗时: %dms\n", result.AnalysisTime)
		fmt.Printf("   分析状态: %v\n", result.Success)

		// 检查DNS信息
		if result.DNS != nil {
			fmt.Printf("   DNS记录: %d个A记录\n", len(result.DNS.ARecords))
			if len(result.DNS.CNAMERecords) > 0 {
				fmt.Printf("   CNAME记录: %v\n", result.DNS.CNAMERecords)
			}
		}

		// 检查ASN信息
		if result.ASN != nil {
			fmt.Printf("   ASN: %s (%s)\n", result.ASN.ASN, result.ASN.ISP)
		}

		// 检查WHOIS信息
		if result.WHOIS != nil {
			fmt.Printf("   WHOIS: 查询%s (耗时: %dms)\n",
				map[bool]string{true: "成功", false: "失败"}[result.WHOIS.Success],
				result.WHOIS.RuntimeMs)
			if result.WHOIS.Company != nil && result.WHOIS.Company.CompanyName != "" {
				fmt.Printf("   公司信息: %s\n", result.WHOIS.Company.CompanyName)
			}
		}

		// 检查CDN信息
		if result.CDN != nil && result.CDN.Provider != "" {
			fmt.Printf("   CDN: %s (置信度: %d%%)\n", result.CDN.Provider, result.CDN.Confidence)
		} else {
			fmt.Printf("   CDN: 未检测到\n")
		}

		// 检查高级分析
		if result.Advanced != nil {
			// 地理位置信息
			if result.Advanced.GeoLocation != nil {
				geo := result.Advanced.GeoLocation
				fmt.Printf("   地理位置: %s, %s (ISP: %s)\n", geo.Country, geo.City, geo.ISP)
			}

			// TLS安全信息
			if result.Advanced.TLSSecurity != nil {
				tls := result.Advanced.TLSSecurity
				fmt.Printf("   TLS安全: %s", tls.SecurityRating)
				if len(tls.SupportedVersions) > 0 {
					fmt.Printf(" (版本: %v)", tls.SupportedVersions)
				}
				fmt.Println()
			}

			// 网站指纹信息
			if result.Advanced.Fingerprint != nil {
				fp := result.Advanced.Fingerprint
				fmt.Printf("   技术栈: %s", fp.ServerType)
				if fp.CMS != "" {
					fmt.Printf(" (CMS: %s)", fp.CMS)
				}
				fmt.Printf(" (置信度: %d%%)\n", fp.Confidence)

				if len(fp.Technologies) > 0 {
					fmt.Printf("   检测到的技术: %v\n", fp.Technologies)
				}
				if len(fp.ProgrammingLanguages) > 0 {
					fmt.Printf("   编程语言: %v\n", fp.ProgrammingLanguages)
				}
			}
		}
	}

	// 2. 测试指标收集
	fmt.Println("\n📊 2. 测试指标收集功能...")
	metricsCollector := network.NewMetricsCollector(network.DefaultMetricsConfig())

	// 模拟一些指标
	for i := 0; i < 5; i++ {
		metricsCollector.RecordRequest()
		if i < 4 { // 4个成功，1个失败
			metricsCollector.RecordSuccess(time.Duration(100+i*50) * time.Millisecond)
			metricsCollector.RecordModuleAnalysis("dns")
			metricsCollector.RecordModuleAnalysis("whois")
			metricsCollector.RecordCacheHit()
		} else {
			metricsCollector.RecordFailure()
			metricsCollector.RecordModuleError("dns")
			metricsCollector.RecordCacheMiss()
		}
		metricsCollector.RecordSecurityScore(80 + i*5)
	}

	snapshot := metricsCollector.GetSnapshot()
	fmt.Printf("✅ 指标收集成功:\n")
	fmt.Printf("   总请求数: %d\n", snapshot.TotalRequests)
	fmt.Printf("   成功请求: %d\n", snapshot.SuccessfulRequests)
	fmt.Printf("   失败请求: %d\n", snapshot.FailedRequests)
	fmt.Printf("   成功率: %.2f%%\n", snapshot.SuccessRate)
	fmt.Printf("   平均响应时间: %.2f ms\n", snapshot.AvgResponseTime)
	fmt.Printf("   缓存命中率: %.2f%%\n", snapshot.CacheHitRate)
	fmt.Printf("   平均安全评分: %.2f\n", snapshot.AvgSecurityScore)

	// 显示模块指标
	fmt.Printf("   模块分析次数:\n")
	for module, count := range snapshot.ModuleMetrics {
		if count > 0 {
			fmt.Printf("     %s: %d次\n", module, count)
		}
	}

	fmt.Printf("   模块错误次数:\n")
	for module, count := range snapshot.ModuleErrors {
		if count > 0 {
			fmt.Printf("     %s: %d次\n", module, count)
		}
	}

	// 3. 测试AI威胁分析
	fmt.Println("\n🤖 3. 测试AI威胁分析功能...")
	securityModel := network.NewMockSecurityModel()
	aiAnalyzer := network.NewAIAnalyzer(network.DefaultAIConfig(), securityModel)

	// 使用第一个成功的分析结果进行AI分析
	if len(testDomains) > 0 {
		result, err := analyzer.Analyze(ctx, testDomains[0])
		if err == nil && result.Success {
			threatAnalysis, err := aiAnalyzer.AnalyzeWithAI(ctx, result)
			if err != nil {
				log.Printf("❌ AI分析失败: %v", err)
			} else {
				fmt.Printf("✅ AI威胁分析成功:\n")
				fmt.Printf("   分析目标: %s\n", result.Domain)
				fmt.Printf("   威胁等级: %s\n", threatAnalysis.ThreatLevel)
				fmt.Printf("   风险评分: %d\n", threatAnalysis.RiskScore)
				fmt.Printf("   置信度: %.2f\n", threatAnalysis.Confidence)
				fmt.Printf("   威胁类型数量: %d\n", len(threatAnalysis.ThreatTypes))
				fmt.Printf("   威胁指标数量: %d\n", len(threatAnalysis.Indicators))
				fmt.Printf("   安全建议数量: %d\n", len(threatAnalysis.Recommendations))

				// 生成安全报告
				securityReport, err := aiAnalyzer.GenerateSecurityReport(ctx, threatAnalysis)
				if err != nil {
					log.Printf("❌ 报告生成失败: %v", err)
				} else {
					fmt.Printf("✅ 安全报告生成成功:\n")
					fmt.Printf("   报告摘要: %s\n", securityReport.Summary)
					fmt.Printf("   安全评分: %d\n", securityReport.SecurityScore)
					fmt.Printf("   建议数量: %d\n", len(securityReport.Recommendations))
					fmt.Printf("   报告语言: %s\n", securityReport.Language)

					// 显示第一个建议
					if len(securityReport.Recommendations) > 0 {
						rec := securityReport.Recommendations[0]
						fmt.Printf("   首个建议: [%s] %s\n", rec.Priority, rec.Title)
					}
				}
			}
		}
	}

	// 4. 测试批量分析
	fmt.Println("\n📦 4. 测试批量分析功能...")
	batchUrls := []string{"www.baidu.com", "***************"}

	fmt.Printf("开始批量分析 %d 个目标...\n", len(batchUrls))
	batchStart := time.Now()

	batchResults, err := analyzer.BatchAnalyze(ctx, batchUrls)
	batchDuration := time.Since(batchStart)

	if err != nil {
		log.Printf("❌ 批量分析失败: %v", err)
	} else {
		successCount := 0
		totalAnalysisTime := 0

		for _, result := range batchResults {
			if result.Success {
				successCount++
				totalAnalysisTime += result.AnalysisTime
			}
		}

		fmt.Printf("✅ 批量分析完成:\n")
		fmt.Printf("   总耗时: %v\n", batchDuration)
		fmt.Printf("   成功率: %d/%d (%.1f%%)\n", successCount, len(batchUrls),
			float64(successCount)/float64(len(batchUrls))*100)
		fmt.Printf("   平均分析时间: %dms\n", totalAnalysisTime/len(batchUrls))

		// 显示每个结果的详情
		for i, result := range batchResults {
			status := "✅"
			if !result.Success {
				status = "❌"
			}
			fmt.Printf("   结果 %d: %s %s -> %s (%dms)\n",
				i+1, status, result.Domain, result.IP, result.AnalysisTime)
		}
	}

	// 5. 测试配置选项
	fmt.Println("\n⚙️  5. 测试配置选项功能...")

	// 创建自定义配置的分析器
	customAnalyzer := network.NewAnalyzer(
		network.WithGeoLocation(false),         // 禁用地理位置
		network.WithTLSSecurity(true),          // 启用TLS
		network.WithFingerprint(false),         // 禁用指纹识别
		network.WithWHOISLookup(true),          // 启用WHOIS
		network.WithCDNDetection(false),        // 禁用CDN检测
		network.WithTimeout(10*time.Second),    // 短超时
		network.WithRetry(2),                   // 2次重试
		network.WithUserAgent("TestAgent/1.0"), // 自定义UA
	)

	fmt.Println("✅ 自定义配置分析器创建成功")

	// 测试自定义配置
	testResult, err := customAnalyzer.Analyze(ctx, "www.baidu.com")
	if err != nil {
		log.Printf("❌ 自定义配置分析失败: %v", err)
	} else {
		fmt.Printf("✅ 自定义配置分析成功:\n")
		fmt.Printf("   域名: %s\n", testResult.Domain)
		fmt.Printf("   分析耗时: %dms\n", testResult.AnalysisTime)

		// 验证配置是否生效
		hasGeo := testResult.Advanced != nil && testResult.Advanced.GeoLocation != nil
		hasFingerprint := testResult.Advanced != nil && testResult.Advanced.Fingerprint != nil
		hasCDN := testResult.CDN != nil && testResult.CDN.Provider != ""

		fmt.Printf("   地理位置分析: %s (应为禁用)\n", map[bool]string{true: "启用", false: "禁用"}[hasGeo])
		fmt.Printf("   指纹识别: %s (应为禁用)\n", map[bool]string{true: "启用", false: "禁用"}[hasFingerprint])
		fmt.Printf("   CDN检测: %s (应为禁用)\n", map[bool]string{true: "启用", false: "禁用"}[hasCDN])
		fmt.Printf("   WHOIS查询: %s\n", map[bool]string{true: "成功", false: "失败"}[testResult.WHOIS != nil && testResult.WHOIS.Success])
	}

	fmt.Println("\n=== 基础功能测试完成 ===")
	fmt.Println("🎉 所有基础功能都正常工作！")

	// 显示功能总结
	fmt.Println("\n📋 已验证的基础功能:")
	fmt.Println("✅ 单域名分析 - DNS、WHOIS、ASN、地理位置、TLS、指纹识别")
	fmt.Println("✅ 批量并发分析 - 高效处理多个目标")
	fmt.Println("✅ AI威胁分析 - 智能安全评估和报告生成")
	fmt.Println("✅ 指标收集系统 - 完整的性能监控")
	fmt.Println("✅ 配置选项 - 灵活的功能开关和参数调优")
	fmt.Println("✅ 错误处理 - 优雅降级和容错机制")
	fmt.Println("✅ 中文支持 - 完整的中文注释和输出")
}
