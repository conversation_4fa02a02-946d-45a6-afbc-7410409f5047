/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Package main demonstrates how to use the Eino network analysis component
package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/network"
	"github.com/cloudwego/eino/components/network/schema"
)

func main() {
	var (
		url          = flag.String("url", "", "URL to analyze")
		batchFile    = flag.String("batch", "", "File containing URLs to analyze (one per line)")
		outputFile   = flag.String("output", "", "Output file (default: stdout)")
		format       = flag.String("format", "json", "Output format (json)")
		concurrency  = flag.Int("concurrency", 5, "Max concurrent requests for batch processing")
		enableGeo    = flag.Bool("geo", true, "Enable geolocation analysis")
		enableTLS    = flag.Bool("tls", true, "Enable TLS security analysis")
		enableFinger = flag.Bool("fingerprint", true, "Enable website fingerprinting")
		enableWHOIS  = flag.Bool("whois", true, "Enable WHOIS lookup")
		enableCDN    = flag.Bool("cdn", true, "Enable CDN detection")
		timeout      = flag.Duration("timeout", 30*time.Second, "Request timeout")
		verbose      = flag.Bool("verbose", false, "Enable verbose output")
		summary      = flag.Bool("summary", false, "Show summary instead of full output")
	)
	flag.Parse()

	if *url == "" && *batchFile == "" {
		fmt.Println("Usage: network-analysis -url=<url> OR -batch=<file>")
		fmt.Println("\nOptions:")
		flag.PrintDefaults()
		fmt.Println("\nExamples:")
		fmt.Println("  network-analysis -url=https://example.com")
		fmt.Println("  network-analysis -batch=urls.txt -concurrency=10")
		fmt.Println("  network-analysis -url=example.com -geo=false -tls=false")
		os.Exit(1)
	}

	// Create network analyzer with options
	analyzer := network.NewAnalyzer(
		network.WithGeoLocation(*enableGeo),
		network.WithTLSSecurity(*enableTLS),
		network.WithFingerprint(*enableFinger),
		network.WithWHOISLookup(*enableWHOIS),
		network.WithCDNDetection(*enableCDN),
		network.WithTimeout(*timeout),
		network.WithConcurrency(*concurrency, 10),
	)

	ctx := context.Background()

	if *batchFile != "" {
		// Batch processing
		urls, err := readURLsFromFile(*batchFile)
		if err != nil {
			log.Fatalf("Failed to read URLs from file: %v", err)
		}

		if *verbose {
			fmt.Printf("Processing %d URLs with concurrency %d...\n", len(urls), *concurrency)
		}

		results, err := analyzer.BatchAnalyze(ctx, urls)
		if err != nil {
			log.Fatalf("Batch analysis failed: %v", err)
		}

		if *summary {
			outputSummary(results)
		} else {
			if err := outputResults(results, *outputFile, *format); err != nil {
				log.Fatalf("Failed to output results: %v", err)
			}
		}

		if *verbose {
			successCount := 0
			for _, result := range results {
				if result.Success {
					successCount++
				}
			}
			fmt.Printf("Completed: %d/%d successful\n", successCount, len(results))
		}
	} else {
		// Single URL processing
		if *verbose {
			fmt.Printf("Analyzing URL: %s\n", *url)
		}

		result, err := analyzer.Analyze(ctx, *url)
		if err != nil {
			log.Fatalf("Analysis failed: %v", err)
		}

		if *summary {
			outputSummary([]*schema.NetworkResult{result})
		} else {
			if err := outputResults([]*schema.NetworkResult{result}, *outputFile, *format); err != nil {
				log.Fatalf("Failed to output result: %v", err)
			}
		}

		if *verbose {
			fmt.Printf("Analysis completed in %dms\n", result.AnalysisTime)
		}
	}
}

func readURLsFromFile(filename string) ([]string, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	lines := strings.Split(string(data), "\n")
	var urls []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasPrefix(line, "#") {
			urls = append(urls, line)
		}
	}

	return urls, nil
}

func outputResults(results []*schema.NetworkResult, outputFile, format string) error {
	var output []byte
	var err error

	switch format {
	case "json":
		if len(results) == 1 {
			output, err = json.MarshalIndent(results[0], "", "  ")
		} else {
			output, err = json.MarshalIndent(results, "", "  ")
		}
	default:
		return fmt.Errorf("unsupported format: %s", format)
	}

	if err != nil {
		return fmt.Errorf("failed to marshal results: %w", err)
	}

	if outputFile != "" {
		return os.WriteFile(outputFile, output, 0644)
	}

	fmt.Println(string(output))
	return nil
}

func outputSummary(results []*schema.NetworkResult) {
	fmt.Printf("=== Network Analysis Summary ===\n\n")

	for i, result := range results {
		if len(results) > 1 {
			fmt.Printf("Result %d: %s\n", i+1, result.Domain)
		} else {
			fmt.Printf("Domain: %s\n", result.Domain)
		}

		if !result.Success {
			fmt.Printf("  ❌ Analysis failed: %s\n\n", result.Error)
			continue
		}

		fmt.Printf("  ✅ Analysis successful (%dms)\n", result.AnalysisTime)
		fmt.Printf("  🌐 IP: %s\n", result.IP)

		// DNS Info
		if result.DNS != nil && len(result.DNS.ARecords) > 0 {
			fmt.Printf("  📡 DNS: %d A records\n", len(result.DNS.ARecords))
		}

		// ASN Info
		if result.ASN != nil && result.ASN.ASN != "ERROR" {
			fmt.Printf("  🏢 ASN: %s (%s)\n", result.ASN.ASN, result.ASN.ISP)
		}

		// WHOIS Info
		if result.WHOIS != nil && result.WHOIS.Success {
			fmt.Printf("  📋 WHOIS: Available")
			if result.WHOIS.Company != nil && result.WHOIS.Company.CompanyName != "" {
				fmt.Printf(" (%s)", result.WHOIS.Company.CompanyName)
			}
			fmt.Println()
		}

		// CDN Info
		if result.CDN != nil && result.CDN.Provider != "" {
			fmt.Printf("  🚀 CDN: %s (confidence: %d%%)\n", result.CDN.Provider, result.CDN.Confidence)
		}

		// Advanced Analysis
		if result.Advanced != nil {
			// Geolocation
			if result.Advanced.GeoLocation != nil {
				geo := result.Advanced.GeoLocation
				fmt.Printf("  🌍 Location: %s, %s", geo.Country, geo.City)
				if geo.ISP != "" {
					fmt.Printf(" (%s)", geo.ISP)
				}
				fmt.Println()
			}

			// TLS Security
			if result.Advanced.TLSSecurity != nil {
				tls := result.Advanced.TLSSecurity
				fmt.Printf("  🔒 TLS: %s", tls.SecurityRating)
				if len(tls.SupportedVersions) > 0 {
					fmt.Printf(" (versions: %v)", tls.SupportedVersions)
				}
				fmt.Println()
			}

			// Fingerprint
			if result.Advanced.Fingerprint != nil {
				fp := result.Advanced.Fingerprint
				fmt.Printf("  🔍 Tech Stack:")
				if fp.ServerType != "" {
					fmt.Printf(" %s", fp.ServerType)
				}
				if fp.CMS != "" {
					fmt.Printf(", %s", fp.CMS)
				}
				if len(fp.Frameworks) > 0 {
					fmt.Printf(", %v", fp.Frameworks)
				}
				if len(fp.Technologies) > 0 {
					fmt.Printf(", %v", fp.Technologies)
				}
				fmt.Printf(" (confidence: %d%%)\n", fp.Confidence)
			}
		}

		fmt.Println()
	}
}
