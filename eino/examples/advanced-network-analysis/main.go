/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/cloudwego/eino/components/network"
)

func main() {
	// 命令行参数
	var (
		mode         = flag.String("mode", "single", "运行模式: single, batch, stream, chain")
		url          = flag.String("url", "", "要分析的URL")
		urlFile      = flag.String("file", "", "包含URL列表的文件")
		enableAI     = flag.Bool("ai", true, "启用AI威胁分析")
		enableStream = flag.Bool("stream", false, "启用流式处理")
		enableChain  = flag.Bool("chain", false, "启用链式处理")
		enableMetrics = flag.Bool("metrics", true, "启用指标收集")
		output       = flag.String("output", "", "输出文件路径")
		format       = flag.String("format", "json", "输出格式: json, summary")
		timeout      = flag.Duration("timeout", 30*time.Second, "请求超时时间")
		concurrency  = flag.Int("concurrency", 5, "并发数")
	)
	flag.Parse()

	if *url == "" && *urlFile == "" {
		fmt.Println("使用方法:")
		fmt.Println("  单个分析: go run main.go -mode=single -url=example.com")
		fmt.Println("  批量分析: go run main.go -mode=batch -file=urls.txt")
		fmt.Println("  流式分析: go run main.go -mode=stream -stream=true")
		fmt.Println("  链式分析: go run main.go -mode=chain -url=example.com -chain=true")
		fmt.Println("  AI分析:   go run main.go -mode=single -url=example.com -ai=true")
		os.Exit(1)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 处理中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigChan
		fmt.Println("\n收到中断信号，正在优雅关闭...")
		cancel()
	}()

	// 创建基础网络分析器
	analyzer := network.NewAnalyzer(
		network.WithGeoLocation(true),
		network.WithTLSSecurity(true),
		network.WithFingerprint(true),
		network.WithWHOISLookup(true),
		network.WithCDNDetection(true),
		network.WithTimeout(*timeout),
		network.WithConcurrency(*concurrency, 10),
	)

	// 创建指标收集器
	var metricsCollector *network.MetricsCollector
	if *enableMetrics {
		metricsCollector = network.NewMetricsCollector(network.DefaultMetricsConfig())
		fmt.Println("✅ 指标收集已启用")
	}

	// 创建AI分析器
	var aiAnalyzer *network.AIAnalyzer
	if *enableAI {
		securityModel := network.NewMockSecurityModel()
		aiAnalyzer = network.NewAIAnalyzer(network.DefaultAIConfig(), securityModel)
		fmt.Println("✅ AI威胁分析已启用")
	}

	// 根据模式执行不同的分析
	switch *mode {
	case "single":
		err := runSingleAnalysis(ctx, analyzer, aiAnalyzer, metricsCollector, *url, *format, *output)
		if err != nil {
			log.Fatalf("单个分析失败: %v", err)
		}

	case "batch":
		urls := []string{"example.com", "google.com", "github.com"} // 简化示例
		if *urlFile != "" {
			// 实际应用中应该从文件读取URL列表
			fmt.Printf("注意: 文件读取功能未实现，使用默认URL列表\n")
		}
		err := runBatchAnalysis(ctx, analyzer, aiAnalyzer, metricsCollector, urls, *format, *output)
		if err != nil {
			log.Fatalf("批量分析失败: %v", err)
		}

	case "stream":
		if !*enableStream {
			log.Fatal("流式模式需要启用 -stream=true")
		}
		err := runStreamAnalysis(ctx, analyzer, metricsCollector)
		if err != nil {
			log.Fatalf("流式分析失败: %v", err)
		}

	case "chain":
		if !*enableChain {
			log.Fatal("链式模式需要启用 -chain=true")
		}
		err := runChainAnalysis(ctx, analyzer, aiAnalyzer, *url, *format, *output)
		if err != nil {
			log.Fatalf("链式分析失败: %v", err)
		}

	default:
		log.Fatalf("未知模式: %s", *mode)
	}

	// 显示最终指标
	if metricsCollector != nil {
		displayMetrics(metricsCollector)
	}
}

// runSingleAnalysis 运行单个分析
func runSingleAnalysis(ctx context.Context, analyzer *network.Analyzer, aiAnalyzer *network.AIAnalyzer, 
	metrics *network.MetricsCollector, url, format, output string) error {
	
	fmt.Printf("🔍 开始分析: %s\n", url)
	
	if metrics != nil {
		metrics.RecordRequest()
	}
	
	start := time.Now()
	result, err := analyzer.Analyze(ctx, url)
	duration := time.Since(start)
	
	if err != nil {
		if metrics != nil {
			metrics.RecordFailure()
		}
		return fmt.Errorf("网络分析失败: %w", err)
	}
	
	if metrics != nil {
		metrics.RecordSuccess(duration)
		metrics.RecordModuleAnalysis("dns")
		metrics.RecordModuleAnalysis("whois")
		metrics.RecordModuleAnalysis("tls")
	}
	
	// AI威胁分析
	var threatAnalysis *network.ThreatAnalysis
	var securityReport *network.SecurityReport
	
	if aiAnalyzer != nil {
		fmt.Println("🤖 执行AI威胁分析...")
		threatAnalysis, err = aiAnalyzer.AnalyzeWithAI(ctx, result)
		if err != nil {
			fmt.Printf("⚠️  AI分析失败: %v\n", err)
		} else {
			if metrics != nil {
				metrics.RecordModuleAnalysis("ai")
				metrics.RecordThreatDetection(threatAnalysis.ThreatLevel == "high")
				if threatAnalysis.RiskScore > 0 {
					metrics.RecordSecurityScore(100 - threatAnalysis.RiskScore)
				}
			}
			
			// 生成安全报告
			securityReport, err = aiAnalyzer.GenerateSecurityReport(ctx, threatAnalysis)
			if err != nil {
				fmt.Printf("⚠️  报告生成失败: %v\n", err)
			}
		}
	}
	
	// 输出结果
	return outputResults(result, threatAnalysis, securityReport, format, output)
}

// runBatchAnalysis 运行批量分析
func runBatchAnalysis(ctx context.Context, analyzer *network.Analyzer, aiAnalyzer *network.AIAnalyzer,
	metrics *network.MetricsCollector, urls []string, format, output string) error {
	
	fmt.Printf("📊 开始批量分析 %d 个URL...\n", len(urls))
	
	start := time.Now()
	results, err := analyzer.BatchAnalyze(ctx, urls)
	duration := time.Since(start)
	
	if err != nil {
		return fmt.Errorf("批量分析失败: %w", err)
	}
	
	// 统计结果
	successCount := 0
	for _, result := range results {
		if metrics != nil {
			metrics.RecordRequest()
			if result.Success {
				metrics.RecordSuccess(time.Duration(result.AnalysisTime) * time.Millisecond)
				successCount++
			} else {
				metrics.RecordFailure()
			}
		}
	}
	
	fmt.Printf("✅ 批量分析完成: %d/%d 成功, 总耗时: %v\n", 
		successCount, len(urls), duration)
	
	// 输出批量结果
	batchResult := &network.NetworkBatchResult{
		Results:    results,
		TotalCount: len(urls),
		Success:    successCount,
		Failed:     len(urls) - successCount,
		StartTime:  start.Unix(),
		EndTime:    time.Now().Unix(),
		Duration:   int(duration.Milliseconds()),
	}
	
	return outputBatchResults(batchResult, format, output)
}

// runStreamAnalysis 运行流式分析
func runStreamAnalysis(ctx context.Context, analyzer *network.Analyzer, metrics *network.MetricsCollector) error {
	fmt.Println("🌊 启动流式分析...")
	
	// 创建流式分析器
	streamConfig := network.DefaultStreamConfig()
	streamAnalyzer := network.NewStreamAnalyzer(analyzer, streamConfig)
	
	// 启动流式分析器
	if err := streamAnalyzer.Start(ctx); err != nil {
		return fmt.Errorf("启动流式分析器失败: %w", err)
	}
	defer streamAnalyzer.Stop()
	
	// 模拟提交URL
	testUrls := []string{"example.com", "google.com", "github.com", "cloudflare.com"}
	go func() {
		for _, url := range testUrls {
			if err := streamAnalyzer.Submit(url); err != nil {
				fmt.Printf("提交URL失败: %v\n", err)
			}
			time.Sleep(2 * time.Second) // 模拟间隔提交
		}
	}()
	
	// 处理结果
	resultCount := 0
	for {
		select {
		case <-ctx.Done():
			fmt.Println("流式分析被中断")
			return nil
			
		case result := <-streamAnalyzer.Results():
			resultCount++
			fmt.Printf("📥 收到结果 %d: %s (成功: %v)\n", 
				result.Index, result.Result.Domain, result.Result.Success)
			
			if metrics != nil && result.Result.Success {
				metrics.RecordSuccess(time.Duration(result.Result.AnalysisTime) * time.Millisecond)
			}
			
			// 处理完所有测试URL后退出
			if resultCount >= len(testUrls) {
				fmt.Println("✅ 流式分析完成")
				return nil
			}
			
		case err := <-streamAnalyzer.Errors():
			fmt.Printf("❌ 流式分析错误: %v\n", err)
			
		case <-time.After(30 * time.Second):
			fmt.Println("⏰ 流式分析超时")
			return nil
		}
	}
}

// runChainAnalysis 运行链式分析
func runChainAnalysis(ctx context.Context, analyzer *network.Analyzer, aiAnalyzer *network.AIAnalyzer,
	url, format, output string) error {
	
	fmt.Printf("🔗 开始链式分析: %s\n", url)
	
	// 创建链式分析器
	chainConfig := network.DefaultChainConfig()
	chainAnalyzer := network.NewChainableNetworkAnalyzer(analyzer, aiAnalyzer, chainConfig)
	
	// 准备输入
	chainInput := &network.ChainInput{
		URL:      url,
		Metadata: map[string]string{"source": "cli"},
		Options:  []network.Option{},
	}
	
	// 执行链式分析
	chainOutput, err := chainAnalyzer.Invoke(ctx, chainInput)
	if err != nil {
		return fmt.Errorf("链式分析失败: %w", err)
	}
	
	// 输出链式结果
	return outputChainResults(chainOutput, format, output)
}

// outputResults 输出单个分析结果
func outputResults(result *network.NetworkResult, threat *network.ThreatAnalysis, 
	report *network.SecurityReport, format, output string) error {
	
	switch format {
	case "json":
		data := map[string]any{
			"network_result":   result,
			"threat_analysis":  threat,
			"security_report":  report,
		}
		return outputJSON(data, output)
		
	case "summary":
		return outputSummary(result, threat, report, output)
		
	default:
		return fmt.Errorf("不支持的输出格式: %s", format)
	}
}

// outputBatchResults 输出批量结果
func outputBatchResults(batchResult *network.NetworkBatchResult, format, output string) error {
	switch format {
	case "json":
		return outputJSON(batchResult, output)
	case "summary":
		fmt.Printf("批量分析摘要:\n")
		fmt.Printf("  总数: %d\n", batchResult.TotalCount)
		fmt.Printf("  成功: %d\n", batchResult.Success)
		fmt.Printf("  失败: %d\n", batchResult.Failed)
		fmt.Printf("  耗时: %d ms\n", batchResult.Duration)
		return nil
	default:
		return fmt.Errorf("不支持的输出格式: %s", format)
	}
}

// outputChainResults 输出链式结果
func outputChainResults(chainOutput *network.ChainOutput, format, output string) error {
	switch format {
	case "json":
		return outputJSON(chainOutput, output)
	case "summary":
		fmt.Printf("链式分析摘要:\n")
		fmt.Printf("  成功: %v\n", chainOutput.Success)
		if chainOutput.NetworkResult != nil {
			fmt.Printf("  域名: %s\n", chainOutput.NetworkResult.Domain)
			fmt.Printf("  IP: %s\n", chainOutput.NetworkResult.IP)
		}
		if chainOutput.ThreatAnalysis != nil {
			fmt.Printf("  威胁等级: %s\n", chainOutput.ThreatAnalysis.ThreatLevel)
			fmt.Printf("  风险评分: %d\n", chainOutput.ThreatAnalysis.RiskScore)
		}
		return nil
	default:
		return fmt.Errorf("不支持的输出格式: %s", format)
	}
}

// outputJSON 输出JSON格式
func outputJSON(data any, output string) error {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %w", err)
	}
	
	if output != "" {
		return os.WriteFile(output, jsonData, 0644)
	}
	
	fmt.Println(string(jsonData))
	return nil
}

// outputSummary 输出摘要格式
func outputSummary(result *network.NetworkResult, threat *network.ThreatAnalysis, 
	report *network.SecurityReport, output string) error {
	
	summary := fmt.Sprintf(`
=== 网络分析摘要 ===
域名: %s
IP地址: %s
分析耗时: %d ms
分析状态: %v

`, result.Domain, result.IP, result.AnalysisTime, result.Success)
	
	if threat != nil {
		summary += fmt.Sprintf(`
=== AI威胁分析 ===
威胁等级: %s
风险评分: %d
置信度: %.2f
威胁类型: %v

`, threat.ThreatLevel, threat.RiskScore, threat.Confidence, threat.ThreatTypes)
	}
	
	if output != "" {
		return os.WriteFile(output, []byte(summary), 0644)
	}
	
	fmt.Print(summary)
	return nil
}

// displayMetrics 显示指标
func displayMetrics(metrics *network.MetricsCollector) {
	snapshot := metrics.GetSnapshot()
	
	fmt.Printf(`
=== 性能指标 ===
总请求数: %d
成功请求: %d
失败请求: %d
成功率: %.2f%%
平均响应时间: %.2f ms
缓存命中率: %.2f%%
当前并发数: %d
最大并发数: %d
运行时间: %.2f 秒

`, snapshot.TotalRequests, snapshot.SuccessfulRequests, snapshot.FailedRequests,
	snapshot.SuccessRate, snapshot.AvgResponseTime, snapshot.CacheHitRate,
	snapshot.ConcurrentRequests, snapshot.MaxConcurrency, snapshot.Uptime)
}
