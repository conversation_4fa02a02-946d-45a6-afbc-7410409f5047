/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"
	"testing"
	"time"
)

func TestAnalyzer_ExtendedFeatures(t *testing.T) {
	// Create analyzer with all features enabled
	analyzer := NewAnalyzer(
		WithGeoLocation(true),
		WithTLSSecurity(true),
		WithFingerprint(true),
		WithWHOISLookup(true),
		WithCDNDetection(true),
		WithTimeout(30*time.Second),
	)

	ctx := context.Background()

	t.Run("TestAnalyze_WithWHOIS", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "example.com")
		if err != nil {
			t.Fatalf("Analysis failed: %v", err)
		}

		if result == nil {
			t.Fatal("Result is nil")
		}

		if result.Domain != "example.com" {
			t.Errorf("Expected domain 'example.com', got '%s'", result.Domain)
		}

		// Check if WHOIS information is present
		if result.WHOIS == nil {
			t.Error("WHOIS information is missing")
		} else {
			t.Logf("WHOIS Success: %v", result.WHOIS.Success)
			if result.WHOIS.Success && result.WHOIS.Company != nil {
				t.Logf("Company: %s", result.WHOIS.Company.CompanyName)
			}
		}
	})

	t.Run("TestAnalyze_WithCDN", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "cloudflare.com")
		if err != nil {
			t.Fatalf("Analysis failed: %v", err)
		}

		if result.CDN == nil {
			t.Error("CDN information is missing")
		} else {
			t.Logf("CDN Provider: %s, Confidence: %d", result.CDN.Provider, result.CDN.Confidence)
			if result.CDN.Provider == "" {
				t.Log("CDN provider not detected (this may be expected)")
			}
		}
	})

	t.Run("TestAnalyze_WithGeolocation", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "google.com")
		if err != nil {
			t.Fatalf("Analysis failed: %v", err)
		}

		if result.Advanced == nil || result.Advanced.GeoLocation == nil {
			t.Error("Geolocation information is missing")
		} else {
			geo := result.Advanced.GeoLocation
			t.Logf("Geolocation: %s, %s, %s", geo.Country, geo.City, geo.ISP)
			if geo.Country == "" {
				t.Error("Country information is missing")
			}
		}
	})

	t.Run("TestAnalyze_WithFingerprint", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "github.com")
		if err != nil {
			t.Fatalf("Analysis failed: %v", err)
		}

		if result.Advanced == nil || result.Advanced.Fingerprint == nil {
			t.Error("Fingerprint information is missing")
		} else {
			fp := result.Advanced.Fingerprint
			t.Logf("Server: %s %s, CMS: %s, Confidence: %d", 
				fp.ServerType, fp.ServerVersion, fp.CMS, fp.Confidence)
			t.Logf("Technologies: %v", fp.Technologies)
			t.Logf("Frameworks: %v", fp.Frameworks)
		}
	})

	t.Run("TestAnalyze_ComprehensiveAnalysis", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "https://www.cloudflare.com")
		if err != nil {
			t.Fatalf("Analysis failed: %v", err)
		}

		// Verify all components are present
		if result.DNS == nil {
			t.Error("DNS information is missing")
		}
		if result.ASN == nil {
			t.Error("ASN information is missing")
		}
		if result.WHOIS == nil {
			t.Error("WHOIS information is missing")
		}
		if result.CDN == nil {
			t.Error("CDN information is missing")
		}
		if result.Advanced == nil {
			t.Error("Advanced analysis is missing")
		} else {
			if result.Advanced.GeoLocation == nil {
				t.Error("Geolocation is missing")
			}
			if result.Advanced.TLSSecurity == nil {
				t.Error("TLS security analysis is missing")
			}
			if result.Advanced.Fingerprint == nil {
				t.Error("Fingerprint analysis is missing")
			}
		}

		t.Logf("Analysis completed in %d ms", result.AnalysisTime)
		t.Logf("Success: %v", result.Success)
	})
}

func TestAnalyzer_BatchExtendedAnalysis(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(true),
		WithTLSSecurity(true),
		WithFingerprint(true),
		WithWHOISLookup(true),
		WithCDNDetection(true),
		WithConcurrency(3, 2),
		WithTimeout(30*time.Second),
	)

	ctx := context.Background()
	urls := []string{
		"example.com",
		"google.com",
		"github.com",
		"cloudflare.com",
	}

	results, err := analyzer.BatchAnalyze(ctx, urls)
	if err != nil {
		t.Fatalf("Batch analysis failed: %v", err)
	}

	if len(results) != len(urls) {
		t.Fatalf("Expected %d results, got %d", len(urls), len(results))
	}

	successCount := 0
	for i, result := range results {
		if result == nil {
			t.Errorf("Result %d is nil", i)
			continue
		}

		t.Logf("Result %d: %s - Success: %v", i, result.Domain, result.Success)
		
		if result.Success {
			successCount++
			
			// Check if extended features are working
			if result.WHOIS != nil && result.WHOIS.Success {
				t.Logf("  WHOIS: Success")
			}
			if result.CDN != nil && result.CDN.Provider != "" {
				t.Logf("  CDN: %s (confidence: %d)", result.CDN.Provider, result.CDN.Confidence)
			}
			if result.Advanced != nil {
				if result.Advanced.GeoLocation != nil {
					t.Logf("  Geo: %s, %s", result.Advanced.GeoLocation.Country, result.Advanced.GeoLocation.City)
				}
				if result.Advanced.Fingerprint != nil {
					t.Logf("  Fingerprint: %s (confidence: %d)", result.Advanced.Fingerprint.ServerType, result.Advanced.Fingerprint.Confidence)
				}
			}
		} else {
			t.Logf("  Error: %s", result.Error)
		}
	}

	if successCount == 0 {
		t.Error("No successful analyses")
	} else {
		t.Logf("Successfully analyzed %d/%d URLs", successCount, len(urls))
	}
}

func TestAnalyzer_IPAnalysis(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(true),
		WithWHOISLookup(true),
		WithTimeout(30*time.Second),
	)

	ctx := context.Background()

	t.Run("TestAnalyze_PublicIP", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "*******")
		if err != nil {
			t.Fatalf("IP analysis failed: %v", err)
		}

		if !result.IsIPAccess {
			t.Error("Expected IsIPAccess to be true for IP input")
		}

		if result.IP != "*******" {
			t.Errorf("Expected IP '*******', got '%s'", result.IP)
		}

		// Check WHOIS for IP
		if result.WHOIS == nil {
			t.Error("WHOIS information is missing for IP")
		} else {
			t.Logf("IP WHOIS Success: %v", result.WHOIS.Success)
			if result.WHOIS.Success && result.WHOIS.Company != nil {
				t.Logf("Organization: %s", result.WHOIS.Company.CompanyName)
			}
		}

		// Check geolocation for IP
		if result.Advanced == nil || result.Advanced.GeoLocation == nil {
			t.Error("Geolocation information is missing for IP")
		} else {
			geo := result.Advanced.GeoLocation
			t.Logf("IP Geolocation: %s, %s", geo.Country, geo.ISP)
		}
	})
}

func TestAnalyzer_ErrorHandling(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(true),
		WithTLSSecurity(true),
		WithFingerprint(true),
		WithWHOISLookup(true),
		WithCDNDetection(true),
		WithTimeout(5*time.Second), // Short timeout to test error handling
	)

	ctx := context.Background()

	t.Run("TestAnalyze_InvalidDomain", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "invalid-domain-that-does-not-exist.invalid")
		
		// The analysis should not fail completely, but individual components may fail
		if err != nil {
			t.Logf("Analysis failed as expected: %v", err)
			return
		}

		if result != nil {
			t.Logf("Analysis completed with partial results. Success: %v", result.Success)
			if !result.Success {
				t.Logf("Error: %s", result.Error)
			}
		}
	})

	t.Run("TestAnalyze_PrivateIP", func(t *testing.T) {
		result, err := analyzer.Analyze(ctx, "***********")
		if err != nil {
			t.Fatalf("Private IP analysis failed: %v", err)
		}

		// Check that geolocation handles private IPs correctly
		if result.Advanced != nil && result.Advanced.GeoLocation != nil {
			geo := result.Advanced.GeoLocation
			if geo.Country != "Reserved" {
				t.Logf("Private IP geolocation: %s (may be 'Reserved' or actual location)", geo.Country)
			}
		}
	})
}
