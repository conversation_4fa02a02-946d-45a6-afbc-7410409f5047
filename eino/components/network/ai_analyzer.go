/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// AIAnalyzer AI驱动的网络安全分析器
type AIAnalyzer struct {
	config        *AIConfig
	threatRules   []ThreatRule
	securityModel SecurityModel
}

// AIConfig AI分析器配置
type AIConfig struct {
	ModelName             string        `json:"model_name"`             // AI模型名称
	MaxTokens             int           `json:"max_tokens"`             // 最大令牌数
	Temperature           float64       `json:"temperature"`            // 温度参数
	EnableThreatIntel     bool          `json:"enable_threat_intel"`    // 启用威胁情报
	EnableRiskScoring     bool          `json:"enable_risk_scoring"`    // 启用风险评分
	EnableRecommendations bool          `json:"enable_recommendations"` // 启用安全建议
	CacheResults          bool          `json:"cache_results"`          // 缓存结果
	Timeout               time.Duration `json:"timeout"`                // 超时时间
}

// ThreatRule 威胁检测规则
type ThreatRule struct {
	ID          string   `json:"id"`          // 规则ID
	Name        string   `json:"name"`        // 规则名称
	Description string   `json:"description"` // 规则描述
	Severity    string   `json:"severity"`    // 严重程度
	Patterns    []string `json:"patterns"`    // 匹配模式
	Enabled     bool     `json:"enabled"`     // 是否启用
}

// SecurityModel 安全模型接口
type SecurityModel interface {
	// AnalyzeThreat 分析威胁
	AnalyzeThreat(ctx context.Context, data *schema.NetworkResult) (*ThreatAnalysis, error)
	// GenerateReport 生成报告
	GenerateReport(ctx context.Context, analysis *ThreatAnalysis) (*SecurityReport, error)
	// ScoreRisk 风险评分
	ScoreRisk(ctx context.Context, data *schema.NetworkResult) (int, error)
}

// ThreatAnalysis 威胁分析结果
type ThreatAnalysis struct {
	ThreatLevel     string            `json:"threat_level"`    // 威胁等级
	ThreatTypes     []string          `json:"threat_types"`    // 威胁类型
	Indicators      []ThreatIndicator `json:"indicators"`      // 威胁指标
	Confidence      float64           `json:"confidence"`      // 置信度
	RiskScore       int               `json:"risk_score"`      // 风险评分
	Recommendations []string          `json:"recommendations"` // 安全建议
	AnalysisTime    time.Time         `json:"analysis_time"`   // 分析时间
}

// ThreatIndicator 威胁指标
type ThreatIndicator struct {
	Type        string  `json:"type"`        // 指标类型
	Value       string  `json:"value"`       // 指标值
	Severity    string  `json:"severity"`    // 严重程度
	Confidence  float64 `json:"confidence"`  // 置信度
	Description string  `json:"description"` // 描述
}

// SecurityReport 安全报告
type SecurityReport struct {
	Summary         string                   `json:"summary"`         // 摘要
	ThreatAnalysis  *ThreatAnalysis          `json:"threat_analysis"` // 威胁分析
	SecurityScore   int                      `json:"security_score"`  // 安全评分
	Recommendations []SecurityRecommendation `json:"recommendations"` // 安全建议
	GeneratedAt     time.Time                `json:"generated_at"`    // 生成时间
	Language        string                   `json:"language"`        // 语言
}

// SecurityRecommendation 安全建议
type SecurityRecommendation struct {
	Priority    string `json:"priority"`    // 优先级
	Category    string `json:"category"`    // 类别
	Title       string `json:"title"`       // 标题
	Description string `json:"description"` // 描述
	Action      string `json:"action"`      // 建议操作
}

// DefaultAIConfig 返回默认AI配置
func DefaultAIConfig() *AIConfig {
	return &AIConfig{
		ModelName:             "gpt-4",
		MaxTokens:             2000,
		Temperature:           0.3,
		EnableThreatIntel:     true,
		EnableRiskScoring:     true,
		EnableRecommendations: true,
		CacheResults:          true,
		Timeout:               30 * time.Second,
	}
}

// NewAIAnalyzer 创建新的AI分析器
func NewAIAnalyzer(config *AIConfig, model SecurityModel) *AIAnalyzer {
	if config == nil {
		config = DefaultAIConfig()
	}

	analyzer := &AIAnalyzer{
		config:        config,
		securityModel: model,
		threatRules:   getDefaultThreatRules(),
	}

	return analyzer
}

// AnalyzeWithAI 使用AI进行综合安全分析
func (ai *AIAnalyzer) AnalyzeWithAI(ctx context.Context, result *schema.NetworkResult) (*ThreatAnalysis, error) {
	if ai.securityModel == nil {
		return nil, fmt.Errorf("安全模型未配置")
	}

	// 设置超时
	ctx, cancel := context.WithTimeout(ctx, ai.config.Timeout)
	defer cancel()

	// 执行威胁分析
	analysis, err := ai.securityModel.AnalyzeThreat(ctx, result)
	if err != nil {
		return nil, fmt.Errorf("AI威胁分析失败: %w", err)
	}

	// 应用威胁规则
	ai.applyThreatRules(analysis, result)

	// 计算风险评分
	if ai.config.EnableRiskScoring {
		riskScore, err := ai.securityModel.ScoreRisk(ctx, result)
		if err == nil {
			analysis.RiskScore = riskScore
		}
	}

	analysis.AnalysisTime = time.Now()
	return analysis, nil
}

// GenerateSecurityReport 生成安全报告
func (ai *AIAnalyzer) GenerateSecurityReport(ctx context.Context, analysis *ThreatAnalysis) (*SecurityReport, error) {
	if ai.securityModel == nil {
		return nil, fmt.Errorf("安全模型未配置")
	}

	// 设置超时
	ctx, cancel := context.WithTimeout(ctx, ai.config.Timeout)
	defer cancel()

	// 生成报告
	report, err := ai.securityModel.GenerateReport(ctx, analysis)
	if err != nil {
		return nil, fmt.Errorf("生成安全报告失败: %w", err)
	}

	report.GeneratedAt = time.Now()
	report.Language = "zh-CN"

	return report, nil
}

// applyThreatRules 应用威胁检测规则
func (ai *AIAnalyzer) applyThreatRules(analysis *ThreatAnalysis, result *schema.NetworkResult) {
	for _, rule := range ai.threatRules {
		if !rule.Enabled {
			continue
		}

		// 检查规则是否匹配
		if ai.matchThreatRule(rule, result) {
			// 添加威胁指标
			indicator := ThreatIndicator{
				Type:        rule.Name,
				Value:       result.Domain,
				Severity:    rule.Severity,
				Confidence:  0.8, // 规则匹配的置信度
				Description: rule.Description,
			}
			analysis.Indicators = append(analysis.Indicators, indicator)

			// 更新威胁类型
			if !contains(analysis.ThreatTypes, rule.Name) {
				analysis.ThreatTypes = append(analysis.ThreatTypes, rule.Name)
			}
		}
	}
}

// matchThreatRule 检查威胁规则是否匹配
func (ai *AIAnalyzer) matchThreatRule(rule ThreatRule, result *schema.NetworkResult) bool {
	// 检查域名模式
	for _, pattern := range rule.Patterns {
		if strings.Contains(strings.ToLower(result.Domain), strings.ToLower(pattern)) {
			return true
		}
	}

	// 检查IP模式
	if result.IP != "" {
		for _, pattern := range rule.Patterns {
			if strings.Contains(result.IP, pattern) {
				return true
			}
		}
	}

	// 检查TLS证书异常
	if result.Advanced != nil && result.Advanced.TLSSecurity != nil {
		tls := result.Advanced.TLSSecurity
		if rule.ID == "weak_tls" && tls.SecurityRating == "F" {
			return true
		}
	}

	return false
}

// getDefaultThreatRules 获取默认威胁检测规则
func getDefaultThreatRules() []ThreatRule {
	return []ThreatRule{
		{
			ID:          "suspicious_domain",
			Name:        "可疑域名",
			Description: "域名包含可疑关键词",
			Severity:    "medium",
			Patterns:    []string{"phishing", "malware", "spam", "scam", "fake"},
			Enabled:     true,
		},
		{
			ID:          "weak_tls",
			Name:        "弱TLS配置",
			Description: "TLS配置存在安全风险",
			Severity:    "high",
			Patterns:    []string{},
			Enabled:     true,
		},
		{
			ID:          "suspicious_ip",
			Name:        "可疑IP地址",
			Description: "IP地址在威胁情报黑名单中",
			Severity:    "high",
			Patterns:    []string{"192.168.", "10.", "172.16."},
			Enabled:     false, // 私有IP检测默认关闭
		},
		{
			ID:          "expired_cert",
			Name:        "证书过期",
			Description: "SSL证书已过期或即将过期",
			Severity:    "medium",
			Patterns:    []string{},
			Enabled:     true,
		},
	}
}

// contains 检查字符串切片是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// MockSecurityModel 模拟安全模型实现（用于测试）
type MockSecurityModel struct {
	responses map[string]*ThreatAnalysis
}

// NewMockSecurityModel 创建模拟安全模型
func NewMockSecurityModel() *MockSecurityModel {
	return &MockSecurityModel{
		responses: make(map[string]*ThreatAnalysis),
	}
}

// AnalyzeThreat 模拟威胁分析
func (m *MockSecurityModel) AnalyzeThreat(ctx context.Context, data *schema.NetworkResult) (*ThreatAnalysis, error) {
	// 模拟AI分析逻辑
	analysis := &ThreatAnalysis{
		ThreatLevel:     "low",
		ThreatTypes:     []string{},
		Indicators:      []ThreatIndicator{},
		Confidence:      0.7,
		RiskScore:       30,
		Recommendations: []string{"建议启用HTTPS", "更新TLS配置"},
		AnalysisTime:    time.Now(),
	}

	// 基于域名进行简单的威胁评估
	domain := strings.ToLower(data.Domain)
	if strings.Contains(domain, "malware") || strings.Contains(domain, "phishing") {
		analysis.ThreatLevel = "high"
		analysis.RiskScore = 90
		analysis.ThreatTypes = append(analysis.ThreatTypes, "恶意软件")
	} else if strings.Contains(domain, "suspicious") {
		analysis.ThreatLevel = "medium"
		analysis.RiskScore = 60
		analysis.ThreatTypes = append(analysis.ThreatTypes, "可疑活动")
	}

	return analysis, nil
}

// GenerateReport 模拟报告生成
func (m *MockSecurityModel) GenerateReport(ctx context.Context, analysis *ThreatAnalysis) (*SecurityReport, error) {
	report := &SecurityReport{
		Summary:        fmt.Sprintf("安全分析完成，威胁等级：%s，风险评分：%d", analysis.ThreatLevel, analysis.RiskScore),
		ThreatAnalysis: analysis,
		SecurityScore:  100 - analysis.RiskScore,
		Recommendations: []SecurityRecommendation{
			{
				Priority:    "high",
				Category:    "TLS安全",
				Title:       "升级TLS配置",
				Description: "当前TLS配置存在安全风险，建议升级到TLS 1.3",
				Action:      "联系系统管理员升级TLS配置",
			},
		},
		GeneratedAt: time.Now(),
		Language:    "zh-CN",
	}

	return report, nil
}

// ScoreRisk 模拟风险评分
func (m *MockSecurityModel) ScoreRisk(ctx context.Context, data *schema.NetworkResult) (int, error) {
	score := 20 // 基础分数

	// 基于各种因素调整分数
	if data.Advanced != nil {
		if data.Advanced.TLSSecurity != nil {
			switch data.Advanced.TLSSecurity.SecurityRating {
			case "A":
				score += 0
			case "B":
				score += 10
			case "C":
				score += 20
			case "D":
				score += 40
			case "F":
				score += 60
			}
		}
	}

	// 确保分数在0-100范围内
	if score > 100 {
		score = 100
	}
	if score < 0 {
		score = 0
	}

	return score, nil
}
