/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// StreamAnalyzer 提供流式网络分析功能
type StreamAnalyzer struct {
	analyzer       *Analyzer           // 底层分析器
	config         *StreamConfig       // 流式配置
	inputChan      chan string         // 输入通道
	outputChan     chan *schema.StreamResult // 输出通道
	errorChan      chan error          // 错误通道
	stopChan       chan struct{}       // 停止信号
	wg             sync.WaitGroup      // 等待组
	isRunning      bool                // 运行状态
	mutex          sync.RWMutex        // 读写锁
	processedCount int64               // 已处理计数
	errorCount     int64               // 错误计数
}

// StreamConfig 流式分析配置
type StreamConfig struct {
	BufferSize     int           // 缓冲区大小
	WorkerCount    int           // 工作协程数
	BatchSize      int           // 批处理大小
	FlushInterval  time.Duration // 刷新间隔
	MaxRetries     int           // 最大重试次数
	EnableMetrics  bool          // 启用指标收集
	EnableBackpressure bool      // 启用背压控制
}

// DefaultStreamConfig 返回默认流式配置
func DefaultStreamConfig() *StreamConfig {
	return &StreamConfig{
		BufferSize:         1000,
		WorkerCount:        5,
		BatchSize:          10,
		FlushInterval:      5 * time.Second,
		MaxRetries:         3,
		EnableMetrics:      true,
		EnableBackpressure: true,
	}
}

// NewStreamAnalyzer 创建新的流式分析器
func NewStreamAnalyzer(analyzer *Analyzer, config *StreamConfig) *StreamAnalyzer {
	if config == nil {
		config = DefaultStreamConfig()
	}

	return &StreamAnalyzer{
		analyzer:   analyzer,
		config:     config,
		inputChan:  make(chan string, config.BufferSize),
		outputChan: make(chan *schema.StreamResult, config.BufferSize),
		errorChan:  make(chan error, config.BufferSize),
		stopChan:   make(chan struct{}),
	}
}

// Start 启动流式分析器
func (sa *StreamAnalyzer) Start(ctx context.Context) error {
	sa.mutex.Lock()
	defer sa.mutex.Unlock()

	if sa.isRunning {
		return fmt.Errorf("流式分析器已在运行")
	}

	sa.isRunning = true

	// 启动工作协程
	for i := 0; i < sa.config.WorkerCount; i++ {
		sa.wg.Add(1)
		go sa.worker(ctx, i)
	}

	// 启动指标收集协程
	if sa.config.EnableMetrics {
		sa.wg.Add(1)
		go sa.metricsCollector(ctx)
	}

	return nil
}

// Stop 停止流式分析器
func (sa *StreamAnalyzer) Stop() error {
	sa.mutex.Lock()
	defer sa.mutex.Unlock()

	if !sa.isRunning {
		return fmt.Errorf("流式分析器未在运行")
	}

	// 发送停止信号
	close(sa.stopChan)

	// 等待所有协程结束
	sa.wg.Wait()

	// 关闭通道
	close(sa.inputChan)
	close(sa.outputChan)
	close(sa.errorChan)

	sa.isRunning = false
	return nil
}

// Submit 提交URL进行分析
func (sa *StreamAnalyzer) Submit(url string) error {
	sa.mutex.RLock()
	defer sa.mutex.RUnlock()

	if !sa.isRunning {
		return fmt.Errorf("流式分析器未运行")
	}

	select {
	case sa.inputChan <- url:
		return nil
	default:
		if sa.config.EnableBackpressure {
			return fmt.Errorf("输入缓冲区已满，启用背压控制")
		}
		// 非阻塞模式，丢弃请求
		return fmt.Errorf("输入缓冲区已满，请求被丢弃")
	}
}

// Results 获取结果通道
func (sa *StreamAnalyzer) Results() <-chan *schema.StreamResult {
	return sa.outputChan
}

// Errors 获取错误通道
func (sa *StreamAnalyzer) Errors() <-chan error {
	return sa.errorChan
}

// GetMetrics 获取运行指标
func (sa *StreamAnalyzer) GetMetrics() *StreamMetrics {
	sa.mutex.RLock()
	defer sa.mutex.RUnlock()

	return &StreamMetrics{
		ProcessedCount: sa.processedCount,
		ErrorCount:     sa.errorCount,
		IsRunning:      sa.isRunning,
		BufferUsage:    float64(len(sa.inputChan)) / float64(sa.config.BufferSize),
	}
}

// StreamMetrics 流式分析指标
type StreamMetrics struct {
	ProcessedCount int64   `json:"processed_count"` // 已处理数量
	ErrorCount     int64   `json:"error_count"`     // 错误数量
	IsRunning      bool    `json:"is_running"`      // 是否运行中
	BufferUsage    float64 `json:"buffer_usage"`    // 缓冲区使用率
}

// worker 工作协程
func (sa *StreamAnalyzer) worker(ctx context.Context, workerID int) {
	defer sa.wg.Done()

	batch := make([]string, 0, sa.config.BatchSize)
	ticker := time.NewTicker(sa.config.FlushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			// 处理剩余批次
			if len(batch) > 0 {
				sa.processBatch(ctx, batch, workerID)
			}
			return

		case <-sa.stopChan:
			// 处理剩余批次
			if len(batch) > 0 {
				sa.processBatch(ctx, batch, workerID)
			}
			return

		case url, ok := <-sa.inputChan:
			if !ok {
				// 输入通道已关闭，处理剩余批次
				if len(batch) > 0 {
					sa.processBatch(ctx, batch, workerID)
				}
				return
			}

			batch = append(batch, url)

			// 批次已满，立即处理
			if len(batch) >= sa.config.BatchSize {
				sa.processBatch(ctx, batch, workerID)
				batch = batch[:0] // 重置批次
			}

		case <-ticker.C:
			// 定时刷新，处理未满的批次
			if len(batch) > 0 {
				sa.processBatch(ctx, batch, workerID)
				batch = batch[:0] // 重置批次
			}
		}
	}
}

// processBatch 处理批次
func (sa *StreamAnalyzer) processBatch(ctx context.Context, urls []string, workerID int) {
	if len(urls) == 0 {
		return
	}

	// 批量分析
	results, err := sa.analyzer.BatchAnalyze(ctx, urls)
	if err != nil {
		// 发送错误到错误通道
		select {
		case sa.errorChan <- fmt.Errorf("工作协程 %d 批量分析失败: %w", workerID, err):
		default:
			// 错误通道已满，忽略
		}
		return
	}

	// 发送结果到输出通道
	for i, result := range results {
		streamResult := &schema.StreamResult{
			Result: result,
			Index:  int(sa.processedCount) + i,
			Total:  -1, // 流式处理中总数未知
			EOF:    false,
		}

		select {
		case sa.outputChan <- streamResult:
			sa.processedCount++
		default:
			// 输出通道已满，记录错误
			sa.errorCount++
			select {
			case sa.errorChan <- fmt.Errorf("输出缓冲区已满，结果丢失: %s", urls[i]):
			default:
				// 错误通道也满了，只能忽略
			}
		}
	}
}

// metricsCollector 指标收集协程
func (sa *StreamAnalyzer) metricsCollector(ctx context.Context) {
	defer sa.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-sa.stopChan:
			return
		case <-ticker.C:
			// 这里可以集成Prometheus或其他指标系统
			metrics := sa.GetMetrics()
			// TODO: 发送指标到监控系统
			_ = metrics
		}
	}
}

// BatchSubmit 批量提交URL
func (sa *StreamAnalyzer) BatchSubmit(urls []string) error {
	for _, url := range urls {
		if err := sa.Submit(url); err != nil {
			return fmt.Errorf("提交URL %s 失败: %w", url, err)
		}
	}
	return nil
}

// Flush 刷新所有待处理的数据
func (sa *StreamAnalyzer) Flush(ctx context.Context) error {
	// 等待输入通道为空
	for len(sa.inputChan) > 0 {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(100 * time.Millisecond):
			// 继续等待
		}
	}
	return nil
}
