/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"
	"fmt"

	"github.com/cloudwego/eino/components"
	"github.com/cloudwego/eino/components/network/schema"
)

// ChainableNetworkAnalyzer 可链式调用的网络分析器
type ChainableNetworkAnalyzer struct {
	analyzer    *Analyzer
	aiAnalyzer  *AIAnalyzer
	enableAI    bool
	chainConfig *ChainConfig
}

// ChainConfig 链式配置
type ChainConfig struct {
	EnablePreprocessing  bool `json:"enable_preprocessing"`  // 启用预处理
	EnablePostprocessing bool `json:"enable_postprocessing"` // 启用后处理
	EnableAIAnalysis     bool `json:"enable_ai_analysis"`    // 启用AI分析
	EnableReporting      bool `json:"enable_reporting"`      // 启用报告生成
	PassthroughErrors    bool `json:"passthrough_errors"`    // 错误透传
}

// ChainInput 链式输入
type ChainInput struct {
	URL      string            `json:"url"`      // 要分析的URL
	Metadata map[string]string `json:"metadata"` // 元数据
	Options  []Option          `json:"options"`  // 分析选项
}

// ChainOutput 链式输出
type ChainOutput struct {
	NetworkResult   *schema.NetworkResult `json:"network_result"`   // 网络分析结果
	ThreatAnalysis  *ThreatAnalysis       `json:"threat_analysis"`  // 威胁分析结果
	SecurityReport  *SecurityReport       `json:"security_report"`  // 安全报告
	ProcessingTime  int64                 `json:"processing_time"`  // 处理时间（毫秒）
	ChainMetadata   map[string]any        `json:"chain_metadata"`   // 链式元数据
	Success         bool                  `json:"success"`          // 是否成功
	Error           string                `json:"error,omitempty"`  // 错误信息
}

// DefaultChainConfig 返回默认链式配置
func DefaultChainConfig() *ChainConfig {
	return &ChainConfig{
		EnablePreprocessing:  true,
		EnablePostprocessing: true,
		EnableAIAnalysis:     true,
		EnableReporting:      true,
		PassthroughErrors:    false,
	}
}

// NewChainableNetworkAnalyzer 创建可链式调用的网络分析器
func NewChainableNetworkAnalyzer(analyzer *Analyzer, aiAnalyzer *AIAnalyzer, config *ChainConfig) *ChainableNetworkAnalyzer {
	if config == nil {
		config = DefaultChainConfig()
	}

	return &ChainableNetworkAnalyzer{
		analyzer:    analyzer,
		aiAnalyzer:  aiAnalyzer,
		enableAI:    aiAnalyzer != nil,
		chainConfig: config,
	}
}

// GetType 返回组件类型
func (cna *ChainableNetworkAnalyzer) GetType() string {
	return "ChainableNetworkAnalyzer"
}

// IsCallbacksEnabled 返回是否启用回调
func (cna *ChainableNetworkAnalyzer) IsCallbacksEnabled() bool {
	return false
}

// Invoke 执行链式分析
func (cna *ChainableNetworkAnalyzer) Invoke(ctx context.Context, input *ChainInput) (*ChainOutput, error) {
	output := &ChainOutput{
		ChainMetadata: make(map[string]any),
		Success:       false,
	}

	// 预处理
	if cna.chainConfig.EnablePreprocessing {
		if err := cna.preprocess(ctx, input, output); err != nil {
			if cna.chainConfig.PassthroughErrors {
				return output, err
			}
			output.Error = fmt.Sprintf("预处理失败: %v", err)
			return output, nil
		}
	}

	// 执行网络分析
	networkResult, err := cna.analyzer.Analyze(ctx, input.URL, input.Options...)
	if err != nil {
		if cna.chainConfig.PassthroughErrors {
			return output, fmt.Errorf("网络分析失败: %w", err)
		}
		output.Error = fmt.Sprintf("网络分析失败: %v", err)
		return output, nil
	}

	output.NetworkResult = networkResult

	// AI威胁分析
	if cna.chainConfig.EnableAIAnalysis && cna.enableAI {
		threatAnalysis, err := cna.aiAnalyzer.AnalyzeWithAI(ctx, networkResult)
		if err != nil {
			if cna.chainConfig.PassthroughErrors {
				return output, fmt.Errorf("AI威胁分析失败: %w", err)
			}
			output.ChainMetadata["ai_analysis_error"] = err.Error()
		} else {
			output.ThreatAnalysis = threatAnalysis
		}
	}

	// 生成安全报告
	if cna.chainConfig.EnableReporting && output.ThreatAnalysis != nil {
		securityReport, err := cna.aiAnalyzer.GenerateSecurityReport(ctx, output.ThreatAnalysis)
		if err != nil {
			if cna.chainConfig.PassthroughErrors {
				return output, fmt.Errorf("安全报告生成失败: %w", err)
			}
			output.ChainMetadata["report_generation_error"] = err.Error()
		} else {
			output.SecurityReport = securityReport
		}
	}

	// 后处理
	if cna.chainConfig.EnablePostprocessing {
		if err := cna.postprocess(ctx, input, output); err != nil {
			if cna.chainConfig.PassthroughErrors {
				return output, err
			}
			output.Error = fmt.Sprintf("后处理失败: %v", err)
			return output, nil
		}
	}

	output.Success = true
	return output, nil
}

// preprocess 预处理
func (cna *ChainableNetworkAnalyzer) preprocess(ctx context.Context, input *ChainInput, output *ChainOutput) error {
	// URL验证和标准化
	if input.URL == "" {
		return fmt.Errorf("URL不能为空")
	}

	// 记录预处理信息
	output.ChainMetadata["preprocessing"] = map[string]any{
		"original_url": input.URL,
		"metadata":     input.Metadata,
	}

	return nil
}

// postprocess 后处理
func (cna *ChainableNetworkAnalyzer) postprocess(ctx context.Context, input *ChainInput, output *ChainOutput) error {
	// 计算综合安全评分
	if output.NetworkResult != nil {
		securityScore := cna.calculateSecurityScore(output)
		output.ChainMetadata["security_score"] = securityScore
	}

	// 添加处理摘要
	summary := cna.generateProcessingSummary(output)
	output.ChainMetadata["processing_summary"] = summary

	return nil
}

// calculateSecurityScore 计算综合安全评分
func (cna *ChainableNetworkAnalyzer) calculateSecurityScore(output *ChainOutput) int {
	score := 100 // 基础分数

	// 基于TLS安全评级调整分数
	if output.NetworkResult.Advanced != nil && output.NetworkResult.Advanced.TLSSecurity != nil {
		switch output.NetworkResult.Advanced.TLSSecurity.SecurityRating {
		case "A":
			score -= 0
		case "B":
			score -= 10
		case "C":
			score -= 20
		case "D":
			score -= 40
		case "F":
			score -= 60
		}
	}

	// 基于威胁分析调整分数
	if output.ThreatAnalysis != nil {
		switch output.ThreatAnalysis.ThreatLevel {
		case "low":
			score -= 5
		case "medium":
			score -= 20
		case "high":
			score -= 50
		case "critical":
			score -= 80
		}
	}

	// 确保分数在0-100范围内
	if score < 0 {
		score = 0
	}

	return score
}

// generateProcessingSummary 生成处理摘要
func (cna *ChainableNetworkAnalyzer) generateProcessingSummary(output *ChainOutput) map[string]any {
	summary := map[string]any{
		"network_analysis_completed": output.NetworkResult != nil,
		"ai_analysis_completed":      output.ThreatAnalysis != nil,
		"report_generated":           output.SecurityReport != nil,
		"overall_success":            output.Success,
	}

	if output.NetworkResult != nil {
		summary["analysis_time_ms"] = output.NetworkResult.AnalysisTime
		summary["domain"] = output.NetworkResult.Domain
		summary["ip"] = output.NetworkResult.IP
	}

	if output.ThreatAnalysis != nil {
		summary["threat_level"] = output.ThreatAnalysis.ThreatLevel
		summary["risk_score"] = output.ThreatAnalysis.RiskScore
		summary["threat_types_count"] = len(output.ThreatAnalysis.ThreatTypes)
	}

	return summary
}

// NetworkAnalysisChain 网络分析链构建器
type NetworkAnalysisChain struct {
	steps []ChainStep
}

// ChainStep 链步骤接口
type ChainStep interface {
	Execute(ctx context.Context, input any) (any, error)
	GetName() string
}

// NewNetworkAnalysisChain 创建网络分析链
func NewNetworkAnalysisChain() *NetworkAnalysisChain {
	return &NetworkAnalysisChain{
		steps: make([]ChainStep, 0),
	}
}

// AddStep 添加步骤
func (nac *NetworkAnalysisChain) AddStep(step ChainStep) *NetworkAnalysisChain {
	nac.steps = append(nac.steps, step)
	return nac
}

// Execute 执行链
func (nac *NetworkAnalysisChain) Execute(ctx context.Context, input any) (any, error) {
	current := input

	for i, step := range nac.steps {
		result, err := step.Execute(ctx, current)
		if err != nil {
			return nil, fmt.Errorf("步骤 %d (%s) 执行失败: %w", i, step.GetName(), err)
		}
		current = result
	}

	return current, nil
}

// NetworkAnalysisStep 网络分析步骤
type NetworkAnalysisStep struct {
	analyzer *Analyzer
	name     string
}

// NewNetworkAnalysisStep 创建网络分析步骤
func NewNetworkAnalysisStep(analyzer *Analyzer) *NetworkAnalysisStep {
	return &NetworkAnalysisStep{
		analyzer: analyzer,
		name:     "网络分析",
	}
}

// Execute 执行网络分析
func (nas *NetworkAnalysisStep) Execute(ctx context.Context, input any) (any, error) {
	url, ok := input.(string)
	if !ok {
		return nil, fmt.Errorf("输入类型错误，期望string，得到%T", input)
	}

	return nas.analyzer.Analyze(ctx, url)
}

// GetName 获取步骤名称
func (nas *NetworkAnalysisStep) GetName() string {
	return nas.name
}

// AIAnalysisStep AI分析步骤
type AIAnalysisStep struct {
	aiAnalyzer *AIAnalyzer
	name       string
}

// NewAIAnalysisStep 创建AI分析步骤
func NewAIAnalysisStep(aiAnalyzer *AIAnalyzer) *AIAnalysisStep {
	return &AIAnalysisStep{
		aiAnalyzer: aiAnalyzer,
		name:       "AI威胁分析",
	}
}

// Execute 执行AI分析
func (aas *AIAnalysisStep) Execute(ctx context.Context, input any) (any, error) {
	networkResult, ok := input.(*schema.NetworkResult)
	if !ok {
		return nil, fmt.Errorf("输入类型错误，期望*schema.NetworkResult，得到%T", input)
	}

	return aas.aiAnalyzer.AnalyzeWithAI(ctx, networkResult)
}

// GetName 获取步骤名称
func (aas *AIAnalysisStep) GetName() string {
	return aas.name
}

// 确保实现了components接口
var _ components.Typer = (*ChainableNetworkAnalyzer)(nil)
var _ components.Checker = (*ChainableNetworkAnalyzer)(nil)
