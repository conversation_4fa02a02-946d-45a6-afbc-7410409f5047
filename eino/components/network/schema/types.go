/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package schema

import (
	"io"
)

// IPASNInfo contains ASN (Autonomous System Number) information
type IPASNInfo struct {
	ASN     string `json:"asn"`
	ISP     string `json:"isp"`
	RawData string `json:"raw_data"`
}

// GeoLocationInfo contains geographical location information
type GeoLocationInfo struct {
	IP           string  `json:"ip"`
	Country      string  `json:"country"`
	CountryCode  string  `json:"country_code"`
	Region       string  `json:"region"`
	City         string  `json:"city"`
	PostalCode   string  `json:"postal_code"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	ISP          string  `json:"isp"`
	Organization string  `json:"organization"`
	ASN          string  `json:"asn"`
	Timezone     string  `json:"timezone"`
}

// CipherSuiteInfo contains cipher suite information
type CipherSuiteInfo struct {
	Name     string `json:"name"`
	Strength string `json:"strength"`
}

// TLSSecurityInfo contains TLS security assessment information
type TLSSecurityInfo struct {
	SupportedVersions    []string          `json:"supported_versions"`
	CertificateInfo      map[string]string `json:"certificate_info"`
	CipherSuites         []CipherSuiteInfo `json:"cipher_suites"`
	Vulnerabilities      map[string]bool   `json:"vulnerabilities"`
	SecurityRating       string            `json:"security_rating"`
	Recommendations      []string          `json:"recommendations"`
	ConnectionSuccessful bool              `json:"connection_successful,omitempty"`
	ErrorMessage         string            `json:"error_message,omitempty"`
	UsedMethod           string            `json:"used_method,omitempty"`
	TLSExtensions        []string          `json:"tls_extensions,omitempty"`
	ALPN                 []string          `json:"alpn,omitempty"`
	OCSP                 bool              `json:"ocsp,omitempty"`
	CT                   bool              `json:"ct,omitempty"`
	SessionTickets       bool              `json:"session_tickets,omitempty"`
	SNI                  bool              `json:"sni,omitempty"`
	RetryCount           int               `json:"retry_count,omitempty"`
	TotalTime            int               `json:"total_time,omitempty"`
}

// FingerprintInfo contains website fingerprinting information
type FingerprintInfo struct {
	ServerType           string            `json:"server_type"`
	ServerVersion        string            `json:"server_version"`
	CMS                  string            `json:"cms"`
	CMSVersion           string            `json:"cms_version"`
	Frameworks           []string          `json:"frameworks"`
	JavaScript           []string          `json:"javascript"`
	Analytics            []string          `json:"analytics"`
	Technologies         []string          `json:"technologies"`
	Confidence           int               `json:"confidence"`
	FrameworkVersions    map[string]string `json:"framework_versions,omitempty"`
	ProgrammingLanguages []string          `json:"programming_languages,omitempty"`
	DatabaseTechnologies []string          `json:"database_technologies,omitempty"`
	SecurityFeatures     []string          `json:"security_features,omitempty"`
	ContentHash          string            `json:"content_hash,omitempty"`
	ResponseTime         int               `json:"response_time,omitempty"`
	ResponseSize         int               `json:"response_size,omitempty"`
	HeaderFingerprints   map[string]string `json:"header_fingerprints,omitempty"`
	CookieFingerprints   map[string]string `json:"cookie_fingerprints,omitempty"`
	Headers              map[string]string `json:"headers,omitempty"`
	Cookies              map[string]string `json:"cookies,omitempty"`
}

// ScreenshotInfo contains website screenshot information
type ScreenshotInfo struct {
	FilePath string `json:"file_path"`
	Format   string `json:"format"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	FileSize int64  `json:"file_size"`
}

// CDNInfo contains CDN (Content Delivery Network) information
type CDNInfo struct {
	Provider   string            `json:"provider"`
	Confidence int               `json:"confidence"`
	Headers    map[string]string `json:"headers"`
	CNames     []string          `json:"cnames"`
}

// CompanyInfo contains company/organization information from WHOIS
type CompanyInfo struct {
	CompanyName        string `json:"company_name"`
	LegalPerson        string `json:"legal_person"`
	Status             string `json:"status"`
	RegistrationNumber string `json:"registration_number"`
}

// DNSInfo contains DNS resolution information
type DNSInfo struct {
	ARecords      []string          `json:"a_records"`
	CNAMERecords  []string          `json:"cname_records,omitempty"`
	MXRecords     []string          `json:"mx_records,omitempty"`
	TXTRecords    []string          `json:"txt_records,omitempty"`
	DNSServerUsed string            `json:"dns_server_used"`
	RawHeaders    map[string]string `json:"raw_headers,omitempty"`
}

// WHOISInfo contains WHOIS information
type WHOISInfo struct {
	RawWhois       string       `json:"raw_whois"`
	RuntimeMs      int          `json:"runtime_ms"`
	Success        bool         `json:"success"`
	Company        *CompanyInfo `json:"company,omitempty"`
	RegistrarInfo  string       `json:"registrar_info,omitempty"`
	CreationDate   string       `json:"creation_date,omitempty"`
	ExpirationDate string       `json:"expiration_date,omitempty"`
}

// AdvancedAnalysis contains advanced analysis results
type AdvancedAnalysis struct {
	GeoLocation   *GeoLocationInfo `json:"geo_location,omitempty"`
	TLSSecurity   *TLSSecurityInfo `json:"tls_security,omitempty"`
	Fingerprint   *FingerprintInfo `json:"fingerprint,omitempty"`
	Screenshot    *ScreenshotInfo  `json:"screenshot,omitempty"`
	ThreatIntel   map[string]any   `json:"threat_intel,omitempty"`
	SecurityScore int              `json:"security_score,omitempty"`
}

// NetworkResult represents the complete network analysis result
type NetworkResult struct {
	// Basic information
	Domain     string `json:"domain"`
	IP         string `json:"ip"`
	IsIPAccess bool   `json:"is_ip_access"`
	Port       int    `json:"port,omitempty"`

	// Core analysis results
	DNS   *DNSInfo   `json:"dns"`
	WHOIS *WHOISInfo `json:"whois"`
	ASN   *IPASNInfo `json:"asn"`
	CDN   *CDNInfo   `json:"cdn,omitempty"`
	TLS   string     `json:"tls_pem,omitempty"`

	// Advanced analysis
	Advanced *AdvancedAnalysis `json:"advanced,omitempty"`

	// Metadata
	Timestamp    int64             `json:"timestamp"`
	AnalysisTime int               `json:"analysis_time_ms"`
	Success      bool              `json:"success"`
	Error        string            `json:"error,omitempty"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

// NetworkBatchResult represents results from batch analysis
type NetworkBatchResult struct {
	Results    []*NetworkResult `json:"results"`
	TotalCount int              `json:"total_count"`
	Success    int              `json:"success_count"`
	Failed     int              `json:"failed_count"`
	StartTime  int64            `json:"start_time"`
	EndTime    int64            `json:"end_time"`
	Duration   int              `json:"duration_ms"`
}

// StreamResult wraps a NetworkResult for streaming
type StreamResult struct {
	Result *NetworkResult `json:"result"`
	Index  int            `json:"index"`
	Total  int            `json:"total"`
	EOF    bool           `json:"eof"`
}

// Implement io.Reader for streaming compatibility
func (sr *StreamResult) Read(p []byte) (n int, err error) {
	if sr.EOF {
		return 0, io.EOF
	}
	// This is a simplified implementation
	// In practice, you'd serialize the result to bytes
	return 0, nil
}

// TODO: Implement streaming functions when integrating with eino's streaming system
// These will be implemented using eino's schema.StreamReader
