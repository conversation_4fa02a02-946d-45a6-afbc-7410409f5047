/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package schema

import (
	"io"
)

// IPASNInfo 包含ASN（自治系统号）信息
type IPASNInfo struct {
	ASN     string `json:"asn"`      // ASN号码
	ISP     string `json:"isp"`      // ISP提供商
	RawData string `json:"raw_data"` // 原始数据
}

// GeoLocationInfo 包含地理位置信息
type GeoLocationInfo struct {
	IP           string  `json:"ip"`           // IP地址
	Country      string  `json:"country"`      // 国家
	CountryCode  string  `json:"country_code"` // 国家代码
	Region       string  `json:"region"`       // 地区
	City         string  `json:"city"`         // 城市
	PostalCode   string  `json:"postal_code"`  // 邮政编码
	Latitude     float64 `json:"latitude"`     // 纬度
	Longitude    float64 `json:"longitude"`    // 经度
	ISP          string  `json:"isp"`          // ISP提供商
	Organization string  `json:"organization"` // 组织
	ASN          string  `json:"asn"`          // ASN号码
	Timezone     string  `json:"timezone"`     // 时区
}

// CipherSuiteInfo 包含密码套件信息
type CipherSuiteInfo struct {
	Name     string `json:"name"`     // 密码套件名称
	Strength string `json:"strength"` // 强度等级
}

// TLSSecurityInfo 包含TLS安全评估信息
type TLSSecurityInfo struct {
	SupportedVersions    []string          `json:"supported_versions"`              // 支持的TLS版本
	CertificateInfo      map[string]string `json:"certificate_info"`                // 证书信息
	CipherSuites         []CipherSuiteInfo `json:"cipher_suites"`                   // 密码套件
	Vulnerabilities      map[string]bool   `json:"vulnerabilities"`                 // 漏洞信息
	SecurityRating       string            `json:"security_rating"`                 // 安全评级
	Recommendations      []string          `json:"recommendations"`                 // 安全建议
	ConnectionSuccessful bool              `json:"connection_successful,omitempty"` // 连接是否成功
	ErrorMessage         string            `json:"error_message,omitempty"`         // 错误消息
	UsedMethod           string            `json:"used_method,omitempty"`           // 使用的方法
	TLSExtensions        []string          `json:"tls_extensions,omitempty"`        // TLS扩展
	ALPN                 []string          `json:"alpn,omitempty"`                  // ALPN协议
	OCSP                 bool              `json:"ocsp,omitempty"`                  // OCSP支持
	CT                   bool              `json:"ct,omitempty"`                    // 证书透明度
	SessionTickets       bool              `json:"session_tickets,omitempty"`       // 会话票据
	SNI                  bool              `json:"sni,omitempty"`                   // SNI支持
	RetryCount           int               `json:"retry_count,omitempty"`           // 重试次数
	TotalTime            int               `json:"total_time,omitempty"`            // 总耗时
}

// FingerprintInfo 包含网站指纹识别信息
type FingerprintInfo struct {
	ServerType           string            `json:"server_type"`                     // 服务器类型
	ServerVersion        string            `json:"server_version"`                  // 服务器版本
	CMS                  string            `json:"cms"`                             // 内容管理系统
	CMSVersion           string            `json:"cms_version"`                     // CMS版本
	Frameworks           []string          `json:"frameworks"`                      // Web框架
	JavaScript           []string          `json:"javascript"`                      // JavaScript库
	Analytics            []string          `json:"analytics"`                       // 分析工具
	Technologies         []string          `json:"technologies"`                    // 技术栈
	Confidence           int               `json:"confidence"`                      // 置信度
	FrameworkVersions    map[string]string `json:"framework_versions,omitempty"`    // 框架版本
	ProgrammingLanguages []string          `json:"programming_languages,omitempty"` // 编程语言
	DatabaseTechnologies []string          `json:"database_technologies,omitempty"` // 数据库技术
	SecurityFeatures     []string          `json:"security_features,omitempty"`     // 安全特性
	ContentHash          string            `json:"content_hash,omitempty"`          // 内容哈希
	ResponseTime         int               `json:"response_time,omitempty"`         // 响应时间
	ResponseSize         int               `json:"response_size,omitempty"`         // 响应大小
	HeaderFingerprints   map[string]string `json:"header_fingerprints,omitempty"`   // HTTP头指纹
	CookieFingerprints   map[string]string `json:"cookie_fingerprints,omitempty"`   // Cookie指纹
	Headers              map[string]string `json:"headers,omitempty"`               // HTTP头
	Cookies              map[string]string `json:"cookies,omitempty"`               // Cookie
}

// ScreenshotInfo 包含网站截图信息
type ScreenshotInfo struct {
	FilePath string `json:"file_path"` // 文件路径
	Format   string `json:"format"`    // 图片格式
	Width    int    `json:"width"`     // 宽度
	Height   int    `json:"height"`    // 高度
	FileSize int64  `json:"file_size"` // 文件大小
}

// CDNInfo 包含CDN（内容分发网络）信息
type CDNInfo struct {
	Provider   string            `json:"provider"`   // CDN提供商
	Confidence int               `json:"confidence"` // 置信度
	Headers    map[string]string `json:"headers"`    // HTTP头
	CNames     []string          `json:"cnames"`     // CNAME记录
}

// CompanyInfo 包含来自WHOIS的公司/组织信息
type CompanyInfo struct {
	CompanyName        string `json:"company_name"`        // 公司名称
	LegalPerson        string `json:"legal_person"`        // 法人代表
	Status             string `json:"status"`              // 状态
	RegistrationNumber string `json:"registration_number"` // 注册号
}

// DNSInfo contains DNS resolution information
type DNSInfo struct {
	ARecords      []string          `json:"a_records"`
	CNAMERecords  []string          `json:"cname_records,omitempty"`
	MXRecords     []string          `json:"mx_records,omitempty"`
	TXTRecords    []string          `json:"txt_records,omitempty"`
	DNSServerUsed string            `json:"dns_server_used"`
	RawHeaders    map[string]string `json:"raw_headers,omitempty"`
}

// WHOISInfo contains WHOIS information
type WHOISInfo struct {
	RawWhois       string       `json:"raw_whois"`
	RuntimeMs      int          `json:"runtime_ms"`
	Success        bool         `json:"success"`
	Company        *CompanyInfo `json:"company,omitempty"`
	RegistrarInfo  string       `json:"registrar_info,omitempty"`
	CreationDate   string       `json:"creation_date,omitempty"`
	ExpirationDate string       `json:"expiration_date,omitempty"`
}

// AdvancedAnalysis contains advanced analysis results
type AdvancedAnalysis struct {
	GeoLocation   *GeoLocationInfo `json:"geo_location,omitempty"`
	TLSSecurity   *TLSSecurityInfo `json:"tls_security,omitempty"`
	Fingerprint   *FingerprintInfo `json:"fingerprint,omitempty"`
	Screenshot    *ScreenshotInfo  `json:"screenshot,omitempty"`
	ThreatIntel   map[string]any   `json:"threat_intel,omitempty"`
	SecurityScore int              `json:"security_score,omitempty"`
}

// NetworkResult represents the complete network analysis result
type NetworkResult struct {
	// Basic information
	Domain     string `json:"domain"`
	IP         string `json:"ip"`
	IsIPAccess bool   `json:"is_ip_access"`
	Port       int    `json:"port,omitempty"`

	// Core analysis results
	DNS   *DNSInfo   `json:"dns"`
	WHOIS *WHOISInfo `json:"whois"`
	ASN   *IPASNInfo `json:"asn"`
	CDN   *CDNInfo   `json:"cdn,omitempty"`
	TLS   string     `json:"tls_pem,omitempty"`

	// Advanced analysis
	Advanced *AdvancedAnalysis `json:"advanced,omitempty"`

	// Metadata
	Timestamp    int64             `json:"timestamp"`
	AnalysisTime int               `json:"analysis_time_ms"`
	Success      bool              `json:"success"`
	Error        string            `json:"error,omitempty"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

// NetworkBatchResult represents results from batch analysis
type NetworkBatchResult struct {
	Results    []*NetworkResult `json:"results"`
	TotalCount int              `json:"total_count"`
	Success    int              `json:"success_count"`
	Failed     int              `json:"failed_count"`
	StartTime  int64            `json:"start_time"`
	EndTime    int64            `json:"end_time"`
	Duration   int              `json:"duration_ms"`
}

// StreamResult wraps a NetworkResult for streaming
type StreamResult struct {
	Result *NetworkResult `json:"result"`
	Index  int            `json:"index"`
	Total  int            `json:"total"`
	EOF    bool           `json:"eof"`
}

// Implement io.Reader for streaming compatibility
func (sr *StreamResult) Read(p []byte) (n int, err error) {
	if sr.EOF {
		return 0, io.EOF
	}
	// This is a simplified implementation
	// In practice, you'd serialize the result to bytes
	return 0, nil
}

// TODO: Implement streaming functions when integrating with eino's streaming system
// These will be implemented using eino's schema.StreamReader
