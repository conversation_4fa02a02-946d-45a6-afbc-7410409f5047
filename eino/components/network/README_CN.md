# Eino 网络分析组件

这是从 MCP 项目迁移到 Eino 框架的网络分析组件，提供全面的网络情报收集和安全分析功能。

## 功能特性

### 🔍 核心分析功能
- **DNS 解析**：A记录、CNAME、MX、TXT记录查询
- **ASN 查询**：IPv4/IPv6 自治系统号信息查询
- **TLS 安全分析**：证书分析、安全评级、漏洞检测
- **WHOIS 查询**：域名和IP的WHOIS信息查询（支持缓存）
- **CDN 检测**：主流CDN提供商识别和置信度评估
- **地理位置查询**：IP地理位置、ISP和组织信息
- **网站指纹识别**：技术栈、框架、CMS、安全特性检测

### 🏗️ 架构优势
- **模块化设计**：每个功能模块独立实现，易于维护和扩展
- **错误处理**：完善的错误处理机制，单个模块失败不影响整体分析
- **并发支持**：支持批量并发分析，提高处理效率
- **配置灵活**：丰富的配置选项，支持功能开关和参数调优
- **缓存机制**：WHOIS查询支持智能缓存，减少重复请求

## 快速开始

### 基本使用

```go
package main

import (
    "context"
    "fmt"
    "time"
    
    "github.com/cloudwego/eino/components/network"
)

func main() {
    // 创建网络分析器，启用所有功能
    analyzer := network.NewAnalyzer(
        network.WithGeoLocation(true),    // 启用地理位置分析
        network.WithTLSSecurity(true),    // 启用TLS安全分析
        network.WithFingerprint(true),    // 启用网站指纹识别
        network.WithWHOISLookup(true),    // 启用WHOIS查询
        network.WithCDNDetection(true),   // 启用CDN检测
        network.WithTimeout(30*time.Second), // 设置30秒超时
    )
    
    ctx := context.Background()
    
    // 分析单个域名
    result, err := analyzer.Analyze(ctx, "example.com")
    if err != nil {
        panic(err)
    }
    
    // 输出分析结果
    fmt.Printf("域名: %s\n", result.Domain)
    fmt.Printf("IP地址: %s\n", result.IP)
    fmt.Printf("分析耗时: %dms\n", result.AnalysisTime)
    
    // 检查各项分析结果
    if result.WHOIS != nil && result.WHOIS.Success {
        fmt.Printf("WHOIS查询成功\n")
    }
    
    if result.CDN != nil && result.CDN.Provider != "" {
        fmt.Printf("CDN提供商: %s (置信度: %d%%)\n", 
            result.CDN.Provider, result.CDN.Confidence)
    }
    
    if result.Advanced != nil {
        if result.Advanced.GeoLocation != nil {
            geo := result.Advanced.GeoLocation
            fmt.Printf("地理位置: %s, %s\n", geo.Country, geo.City)
        }
        
        if result.Advanced.Fingerprint != nil {
            fp := result.Advanced.Fingerprint
            fmt.Printf("技术栈: %s (置信度: %d%%)\n", 
                fp.ServerType, fp.Confidence)
        }
    }
}
```

### 批量分析

```go
// 批量分析多个域名
domains := []string{
    "example.com",
    "google.com", 
    "github.com",
    "cloudflare.com",
}

// 配置并发参数
analyzer := network.NewAnalyzer(
    network.WithGeoLocation(true),
    network.WithTLSSecurity(true),
    network.WithFingerprint(true),
    network.WithWHOISLookup(true),
    network.WithCDNDetection(true),
    network.WithConcurrency(5, 10), // 最大5个并发，批处理大小10
    network.WithTimeout(30*time.Second),
)

results, err := analyzer.BatchAnalyze(ctx, domains)
if err != nil {
    panic(err)
}

for i, result := range results {
    fmt.Printf("结果 %d: %s - 成功: %v\n", 
        i+1, result.Domain, result.Success)
}
```

## 配置选项

### 功能开关

```go
analyzer := network.NewAnalyzer(
    // 分析功能开关
    network.WithGeoLocation(true),     // 地理位置分析
    network.WithTLSSecurity(true),     // TLS安全分析  
    network.WithFingerprint(true),     // 网站指纹识别
    network.WithWHOISLookup(true),     // WHOIS查询
    network.WithCDNDetection(true),    // CDN检测
    
    // 网络配置
    network.WithTimeout(30*time.Second),        // 请求超时
    network.WithRetry(3),                       // 重试次数
    network.WithSSLVerification(false),         // SSL证书验证
    
    // 并发配置
    network.WithConcurrency(5, 10),             // 并发数和批处理大小
    
    // 自定义配置
    network.WithUserAgent("MyApp/1.0"),         // 用户代理
    network.WithDNSServers([]string{"*******"}), // DNS服务器
)
```

### 代理配置

```go
analyzer := network.NewAnalyzer(
    network.WithProxy("http://proxy.example.com:8080", "username", "password"),
    // 其他配置...
)
```

## 分析结果结构

### 基本信息
- `Domain`: 域名
- `IP`: IP地址
- `IsIPAccess`: 是否为IP访问
- `Port`: 端口号
- `Timestamp`: 分析时间戳
- `AnalysisTime`: 分析耗时（毫秒）
- `Success`: 分析是否成功
- `Error`: 错误信息（如果有）

### DNS信息 (`result.DNS`)
- `ARecords`: A记录列表
- `CNAMERecords`: CNAME记录列表
- `MXRecords`: MX记录列表
- `TXTRecords`: TXT记录列表

### ASN信息 (`result.ASN`)
- `ASN`: ASN号码
- `ISP`: ISP提供商
- `RawData`: 原始数据

### WHOIS信息 (`result.WHOIS`)
- `Success`: 查询是否成功
- `RawWhois`: 原始WHOIS数据
- `Company`: 公司信息
- `RegistrarInfo`: 注册商信息
- `CreationDate`: 创建日期
- `ExpirationDate`: 过期日期

### CDN信息 (`result.CDN`)
- `Provider`: CDN提供商
- `Confidence`: 置信度（0-100）
- `Headers`: 相关HTTP头
- `CNames`: CNAME记录

### 高级分析 (`result.Advanced`)

#### 地理位置 (`GeoLocation`)
- `Country`: 国家
- `City`: 城市
- `ISP`: ISP提供商
- `Latitude/Longitude`: 经纬度
- `Timezone`: 时区

#### TLS安全 (`TLSSecurity`)
- `SecurityRating`: 安全评级
- `SupportedVersions`: 支持的TLS版本
- `CipherSuites`: 密码套件
- `Vulnerabilities`: 漏洞信息
- `Recommendations`: 安全建议

#### 网站指纹 (`Fingerprint`)
- `ServerType`: 服务器类型
- `CMS`: 内容管理系统
- `Frameworks`: Web框架
- `Technologies`: 技术栈
- `ProgrammingLanguages`: 编程语言
- `SecurityFeatures`: 安全特性
- `Confidence`: 置信度

## 错误处理

组件采用优雅降级的错误处理策略：

```go
result, err := analyzer.Analyze(ctx, "example.com")
if err != nil {
    // 整体分析失败
    log.Printf("分析失败: %v", err)
    return
}

// 检查各个模块的结果
if !result.Success {
    log.Printf("分析部分失败: %s", result.Error)
}

// 单个模块失败不影响其他模块
if result.WHOIS != nil && !result.WHOIS.Success {
    log.Printf("WHOIS查询失败，但其他分析继续")
}
```

## 性能优化

### 缓存机制
- WHOIS查询结果自动缓存24小时
- 支持自定义缓存TTL
- 自动清理过期缓存条目

### 并发控制
- 支持批量并发分析
- 可配置最大并发数
- 信号量控制资源使用

### 超时控制
- 全局超时设置
- 单个请求超时控制
- 上下文取消支持

## 扩展开发

### 添加新的分析模块

1. 在 `internal/` 目录下创建新模块
2. 实现分析接口
3. 在主分析器中集成
4. 添加配置选项

### 自定义数据结构

在 `schema/types.go` 中添加新的数据结构定义。

## 许可证

Apache License 2.0 - 详见 LICENSE 文件
