/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/cloudwego/eino/components"
	"github.com/cloudwego/eino/components/network/internal/asn"
	"github.com/cloudwego/eino/components/network/internal/cdn"
	"github.com/cloudwego/eino/components/network/internal/dns"
	"github.com/cloudwego/eino/components/network/internal/fingerprint"
	"github.com/cloudwego/eino/components/network/internal/geolocation"
	tlsAnalyzer "github.com/cloudwego/eino/components/network/internal/tls"
	"github.com/cloudwego/eino/components/network/internal/whois"
	"github.com/cloudwego/eino/components/network/schema"
)

// Analyzer implements the NetworkAnalyzer interface
type Analyzer struct {
	config              *Config
	dnsResolver         *dns.Resolver
	asnLookup           *asn.Lookup
	tlsAnalyzer         *tlsAnalyzer.Analyzer
	whoisResolver       *whois.Resolver
	cdnDetector         *cdn.Detector
	geoResolver         *geolocation.Resolver
	fingerprintDetector *fingerprint.Detector
}

// NewAnalyzer creates a new network analyzer with the given options
func NewAnalyzer(opts ...Option) *Analyzer {
	config := DefaultConfig()
	ApplyOptions(config, opts...)

	return &Analyzer{
		config:              config,
		dnsResolver:         dns.NewResolver(config.DNSServers, config.Timeout),
		asnLookup:           asn.NewLookup(config.Timeout),
		tlsAnalyzer:         tlsAnalyzer.NewAnalyzer(config.Timeout, !config.VerifySSL),
		whoisResolver:       whois.NewResolver(24*time.Hour, config.Timeout),
		cdnDetector:         cdn.NewDetector(config.Timeout),
		geoResolver:         geolocation.NewResolver(config.Timeout),
		fingerprintDetector: fingerprint.NewDetector(config.Timeout),
	}
}

// GetType returns the component type name
func (a *Analyzer) GetType() string {
	return "NetworkAnalyzer"
}

// IsCallbacksEnabled returns whether callbacks are enabled
func (a *Analyzer) IsCallbacksEnabled() bool {
	return false // Let the framework handle callbacks
}

// Analyze performs comprehensive network analysis on a single URL/domain
func (a *Analyzer) Analyze(ctx context.Context, input string, opts ...Option) (*schema.NetworkResult, error) {
	startTime := time.Now()

	// Apply runtime options
	config := *a.config // Copy config
	ApplyOptions(&config, opts...)

	// Parse URL
	parsedURL, err := url.Parse(input)
	if err != nil {
		return nil, fmt.Errorf("failed to parse URL: %w", err)
	}

	// If no scheme specified, default to HTTPS
	if parsedURL.Scheme == "" {
		input = "https://" + input
		parsedURL, err = url.Parse(input)
		if err != nil {
			return nil, fmt.Errorf("failed to parse URL with scheme: %w", err)
		}
	}

	host := parsedURL.Hostname()
	isIP := dns.IsIPAddress(host)
	port := parsedURL.Port()

	// Set default port if not specified
	portNum := 443
	if port != "" {
		portNum, _ = strconv.Atoi(port)
	} else if parsedURL.Scheme == "http" {
		portNum = 80
	}

	// Create result structure
	result := &schema.NetworkResult{
		Domain:     host,
		IsIPAccess: isIP,
		Port:       portNum,
		Timestamp:  time.Now().Unix(),
		Success:    true,
		Metadata:   make(map[string]string),
	}

	// Perform DNS analysis
	if config.EnableDNSAnalysis && !isIP {
		dnsInfo, err := a.dnsResolver.ResolveDomain(ctx, host)
		if err != nil {
			result.Error = fmt.Sprintf("DNS resolution failed: %v", err)
			result.Success = false
		} else {
			result.DNS = dnsInfo
			if len(dnsInfo.ARecords) > 0 {
				result.IP = dnsInfo.ARecords[0]
			}
		}
	} else if isIP {
		// For IP addresses, set the IP directly
		result.IP = host
		result.DNS = &schema.DNSInfo{
			ARecords: []string{host},
		}
	}

	// Perform ASN lookup
	if result.IP != "" {
		asnInfo, err := a.asnLookup.LookupASN(ctx, result.IP)
		if err != nil {
			// Don't fail the entire analysis for ASN lookup failure
			result.ASN = &schema.IPASNInfo{
				ASN:     "ERROR",
				ISP:     "",
				RawData: fmt.Sprintf("ASN lookup failed: %v", err),
			}
		} else {
			result.ASN = asnInfo
		}
	}

	// Perform WHOIS lookup if enabled
	if config.EnableWHOISLookup {
		var whoisInfo *schema.WHOISInfo
		var err error
		
		if isIP {
			whoisInfo, err = a.whoisResolver.ResolveIP(ctx, host)
		} else {
			whoisInfo, err = a.whoisResolver.ResolveDomain(ctx, host)
		}
		
		if err != nil {
			// Don't fail the entire analysis for WHOIS failure
			result.WHOIS = &schema.WHOISInfo{
				Success: false,
				RawWhois: fmt.Sprintf("WHOIS lookup failed: %v", err),
			}
		} else {
			result.WHOIS = whoisInfo
		}
	}

	// Perform CDN detection if enabled
	if config.EnableCDNDetection {
		cdnInfo, err := a.cdnDetector.DetectCDN(ctx, host)
		if err != nil {
			// Don't fail the entire analysis for CDN detection failure
			result.CDN = &schema.CDNInfo{
				Provider:   "Detection Failed",
				Confidence: 0,
				Headers:    make(map[string]string),
				CNames:     []string{},
			}
		} else {
			result.CDN = cdnInfo
		}
	}

	// Perform geolocation lookup if enabled and we have an IP
	if config.EnableGeoLocation && result.IP != "" {
		geoInfo, err := a.geoResolver.ResolveIP(ctx, result.IP)
		if err != nil {
			// Don't fail the entire analysis for geolocation failure
			if result.Advanced == nil {
				result.Advanced = &schema.AdvancedAnalysis{}
			}
			result.Advanced.GeoLocation = &schema.GeoLocationInfo{
				IP:      result.IP,
				Country: "Unknown",
				City:    "Unknown",
			}
		} else {
			if result.Advanced == nil {
				result.Advanced = &schema.AdvancedAnalysis{}
			}
			result.Advanced.GeoLocation = geoInfo
		}
	}

	// Perform TLS analysis if enabled and we have an IP
	if config.EnableTLSSecurity && result.IP != "" {
		tlsPem, tlsSecurityInfo, err := a.tlsAnalyzer.AnalyzeTLS(ctx, host, portNum)
		if err != nil {
			// Don't fail the entire analysis for TLS failure
			result.TLS = fmt.Sprintf("TLS analysis failed: %v", err)
		} else {
			result.TLS = tlsPem
			if result.Advanced == nil {
				result.Advanced = &schema.AdvancedAnalysis{}
			}
			result.Advanced.TLSSecurity = tlsSecurityInfo
		}
	}

	// Perform fingerprinting if enabled
	if config.EnableFingerprint {
		fingerprintInfo, err := a.fingerprintDetector.DetectFingerprint(ctx, input)
		if err != nil {
			// Don't fail the entire analysis for fingerprinting failure
			if result.Advanced == nil {
				result.Advanced = &schema.AdvancedAnalysis{}
			}
			result.Advanced.Fingerprint = &schema.FingerprintInfo{
				ServerType: "Detection Failed",
				Confidence: 0,
			}
		} else {
			if result.Advanced == nil {
				result.Advanced = &schema.AdvancedAnalysis{}
			}
			result.Advanced.Fingerprint = fingerprintInfo
		}
	}

	// Calculate analysis time
	result.AnalysisTime = int(time.Since(startTime).Milliseconds())

	return result, nil
}

// BatchAnalyze performs analysis on multiple URLs/domains concurrently
func (a *Analyzer) BatchAnalyze(ctx context.Context, inputs []string, opts ...Option) ([]*schema.NetworkResult, error) {
	config := *a.config // Copy config
	ApplyOptions(&config, opts...)

	results := make([]*schema.NetworkResult, len(inputs))
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, config.MaxConcurrency)

	for i, input := range inputs {
		wg.Add(1)
		go func(index int, url string) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			result, err := a.Analyze(ctx, url, opts...)
			if err != nil {
				// Create error result
				result = &schema.NetworkResult{
					Domain:    url,
					Success:   false,
					Error:     err.Error(),
					Timestamp: time.Now().Unix(),
				}
			}
			results[index] = result
		}(i, input)
	}

	wg.Wait()
	return results, nil
}

// firstOrEmpty returns the first element of a slice or empty string if slice is empty
func firstOrEmpty(slice []string) string {
	if len(slice) > 0 {
		return slice[0]
	}
	return ""
}

// Ensure Analyzer implements the required interfaces
var _ NetworkAnalyzer = (*Analyzer)(nil)
var _ components.Typer = (*Analyzer)(nil)
var _ components.Checker = (*Analyzer)(nil)
