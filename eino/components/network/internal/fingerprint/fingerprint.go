/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package fingerprint

import (
	"context"
	"crypto/sha256"
	"crypto/tls"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Detector 提供网站指纹识别功能
type Detector struct {
	client  *http.Client  // HTTP客户端
	timeout time.Duration // 超时时间
}

// NewDetector 创建新的指纹检测器
func NewDetector(timeout time.Duration) *Detector {
	// 创建自定义传输的HTTP客户端
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 跳过证书验证
		},
		DisableKeepAlives: true,
		IdleConnTimeout:   5 * time.Second,
	}

	client := &http.Client{
		Timeout:   timeout,
		Transport: tr,
	}

	return &Detector{
		client:  client,
		timeout: timeout,
	}
}

// DetectFingerprint 分析网站的技术栈和指纹信息
func (d *Detector) DetectFingerprint(ctx context.Context, urlStr string) (*schema.FingerprintInfo, error) {
	// 确保URL有正确的协议
	if !strings.HasPrefix(urlStr, "http://") && !strings.HasPrefix(urlStr, "https://") {
		// 首先尝试HTTPS
		httpsURL := "https://" + urlStr
		fingerprint, err := d.tryDetectFingerprint(ctx, httpsURL)
		if err == nil {
			return fingerprint, nil
		}

		// 如果HTTPS失败，尝试HTTP
		httpURL := "http://" + urlStr
		fingerprint, err = d.tryDetectFingerprint(ctx, httpURL)
		if err == nil {
			return fingerprint, nil
		}

		return nil, fmt.Errorf("HTTP和HTTPS请求都失败: %v", err)
	}

	// URL已经有协议
	return d.tryDetectFingerprint(ctx, urlStr)
}

// tryDetectFingerprint attempts to detect fingerprint for a specific URL
func (d *Detector) tryDetectFingerprint(ctx context.Context, urlStr string) (*schema.FingerprintInfo, error) {
	start := time.Now()

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", urlStr, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	// Make request
	resp, err := d.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	responseTime := int(time.Since(start).Milliseconds())
	responseSize := len(body)

	// Initialize fingerprint
	fingerprint := &schema.FingerprintInfo{
		Headers:              make(map[string]string),
		Cookies:              make(map[string]string),
		FrameworkVersions:    make(map[string]string),
		HeaderFingerprints:   make(map[string]string),
		CookieFingerprints:   make(map[string]string),
		ResponseTime:         responseTime,
		ResponseSize:         responseSize,
		ContentHash:          d.calculateContentHash(body),
		Frameworks:           []string{},
		JavaScript:           []string{},
		Analytics:            []string{},
		Technologies:         []string{},
		ProgrammingLanguages: []string{},
		DatabaseTechnologies: []string{},
		SecurityFeatures:     []string{},
	}

	// Extract headers
	for k, v := range resp.Header {
		fingerprint.Headers[strings.ToLower(k)] = strings.Join(v, "; ")
	}

	// Extract server information
	if server := resp.Header.Get("Server"); server != "" {
		fingerprint.ServerType = d.extractServerType(server)
		fingerprint.ServerVersion = d.extractServerVersion(server)
	}

	// Extract cookies
	for _, cookie := range resp.Cookies() {
		fingerprint.Cookies[cookie.Name] = cookie.Value
	}

	// Analyze page content
	bodyStr := string(body)

	// Detect CMS
	fingerprint.CMS, fingerprint.CMSVersion = d.detectCMS(bodyStr, fingerprint.Headers)

	// Detect frameworks
	fingerprint.Frameworks = d.detectFrameworks(bodyStr)

	// Detect JavaScript libraries
	fingerprint.JavaScript = d.detectJavaScriptLibraries(bodyStr)

	// Detect analytics tools
	fingerprint.Analytics = d.detectAnalytics(bodyStr)

	// Detect technologies
	fingerprint.Technologies = d.detectTechnologies(bodyStr, fingerprint.Headers)

	// Detect programming languages
	fingerprint.ProgrammingLanguages = d.detectProgrammingLanguages(bodyStr, fingerprint.Headers, fingerprint.Cookies)

	// Detect database technologies
	fingerprint.DatabaseTechnologies = d.detectDatabaseTechnologies(bodyStr)

	// Detect security features
	fingerprint.SecurityFeatures = d.detectSecurityFeatures(fingerprint.Headers, bodyStr)

	// Detect framework versions
	fingerprint.FrameworkVersions = d.detectFrameworkVersions(bodyStr, fingerprint.Headers)

	// Analyze header fingerprints
	fingerprint.HeaderFingerprints = d.analyzeHeaderFingerprints(fingerprint.Headers)

	// Analyze cookie fingerprints
	fingerprint.CookieFingerprints = d.analyzeCookieFingerprints(fingerprint.Cookies)

	// Calculate confidence
	fingerprint.Confidence = d.calculateConfidence(fingerprint)

	return fingerprint, nil
}

// calculateContentHash calculates SHA256 hash of content
func (d *Detector) calculateContentHash(content []byte) string {
	hash := sha256.Sum256(content)
	return hex.EncodeToString(hash[:])
}

// extractServerType extracts server type from Server header
func (d *Detector) extractServerType(serverHeader string) string {
	parts := strings.Split(serverHeader, "/")
	if len(parts) > 0 {
		return strings.TrimSpace(parts[0])
	}
	return serverHeader
}

// extractServerVersion extracts server version from Server header
func (d *Detector) extractServerVersion(serverHeader string) string {
	parts := strings.Split(serverHeader, "/")
	if len(parts) > 1 {
		versionPart := strings.Split(parts[1], " ")[0]
		return strings.TrimSpace(versionPart)
	}
	return ""
}

// detectCMS detects Content Management System
func (d *Detector) detectCMS(body string, headers map[string]string) (string, string) {
	bodyLower := strings.ToLower(body)

	// WordPress
	if strings.Contains(bodyLower, "wp-content") || strings.Contains(bodyLower, "wordpress") {
		version := d.extractWordPressVersion(body)
		return "WordPress", version
	}

	// Drupal
	if strings.Contains(bodyLower, "drupal") || strings.Contains(bodyLower, "/sites/default/") {
		return "Drupal", ""
	}

	// Joomla
	if strings.Contains(bodyLower, "joomla") || strings.Contains(bodyLower, "/components/com_") {
		return "Joomla", ""
	}

	// Shopify
	if strings.Contains(bodyLower, "shopify") || strings.Contains(bodyLower, "cdn.shopify.com") {
		return "Shopify", ""
	}

	// Magento
	if strings.Contains(bodyLower, "magento") || strings.Contains(bodyLower, "/skin/frontend/") {
		return "Magento", ""
	}

	return "", ""
}

// extractWordPressVersion extracts WordPress version
func (d *Detector) extractWordPressVersion(body string) string {
	re := regexp.MustCompile(`(?i)wordpress[^0-9]*([0-9]+\.[0-9]+(?:\.[0-9]+)?)`)
	matches := re.FindStringSubmatch(body)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// detectFrameworks detects web frameworks
func (d *Detector) detectFrameworks(body string) []string {
	var frameworks []string
	bodyLower := strings.ToLower(body)

	frameworks = append(frameworks, d.detectInContent(bodyLower, map[string]string{
		"React":       "react",
		"Vue.js":      "vue",
		"Angular":     "angular",
		"jQuery":      "jquery",
		"Bootstrap":   "bootstrap",
		"Foundation":  "foundation",
		"Materialize": "materialize",
	})...)

	return frameworks
}

// detectJavaScriptLibraries detects JavaScript libraries
func (d *Detector) detectJavaScriptLibraries(body string) []string {
	var libraries []string
	bodyLower := strings.ToLower(body)

	libraries = append(libraries, d.detectInContent(bodyLower, map[string]string{
		"jQuery":    "jquery",
		"Lodash":    "lodash",
		"Moment.js": "moment",
		"D3.js":     "d3",
		"Chart.js":  "chart.js",
		"Three.js":  "three.js",
		"Axios":     "axios",
	})...)

	return libraries
}

// detectAnalytics detects analytics tools
func (d *Detector) detectAnalytics(body string) []string {
	var analytics []string
	bodyLower := strings.ToLower(body)

	analytics = append(analytics, d.detectInContent(bodyLower, map[string]string{
		"Google Analytics":   "google-analytics",
		"Google Tag Manager": "gtm",
		"Facebook Pixel":     "fbevents",
		"Hotjar":             "hotjar",
		"Mixpanel":           "mixpanel",
		"Adobe Analytics":    "adobe",
	})...)

	return analytics
}

// detectTechnologies detects various technologies
func (d *Detector) detectTechnologies(body string, headers map[string]string) []string {
	var technologies []string
	bodyLower := strings.ToLower(body)

	// Check headers for technology indicators
	for header, value := range headers {
		valueLower := strings.ToLower(value)
		if strings.Contains(header, "x-powered-by") {
			if strings.Contains(valueLower, "php") {
				technologies = append(technologies, "PHP")
			}
			if strings.Contains(valueLower, "asp.net") {
				technologies = append(technologies, "ASP.NET")
			}
		}
	}

	// Check body content
	technologies = append(technologies, d.detectInContent(bodyLower, map[string]string{
		"Node.js":       "node",
		"Express":       "express",
		"Laravel":       "laravel",
		"Django":        "django",
		"Flask":         "flask",
		"Ruby on Rails": "rails",
		"Spring":        "spring",
	})...)

	return technologies
}

// detectProgrammingLanguages detects programming languages
func (d *Detector) detectProgrammingLanguages(body string, headers map[string]string, cookies map[string]string) []string {
	var languages []string

	// Check headers
	for header, value := range headers {
		valueLower := strings.ToLower(value)
		if strings.Contains(header, "x-powered-by") {
			if strings.Contains(valueLower, "php") {
				languages = append(languages, "PHP")
			}
			if strings.Contains(valueLower, "asp.net") {
				languages = append(languages, "C#")
			}
		}
	}

	// Check cookies for language indicators
	for name := range cookies {
		nameLower := strings.ToLower(name)
		if strings.Contains(nameLower, "phpsessid") {
			languages = append(languages, "PHP")
		}
		if strings.Contains(nameLower, "jsessionid") {
			languages = append(languages, "Java")
		}
		if strings.Contains(nameLower, "asp.net") {
			languages = append(languages, "C#")
		}
	}

	return d.removeDuplicates(languages)
}

// detectDatabaseTechnologies detects database technologies
func (d *Detector) detectDatabaseTechnologies(body string) []string {
	var databases []string
	bodyLower := strings.ToLower(body)

	databases = append(databases, d.detectInContent(bodyLower, map[string]string{
		"MySQL":      "mysql",
		"PostgreSQL": "postgresql",
		"MongoDB":    "mongodb",
		"Redis":      "redis",
		"SQLite":     "sqlite",
		"Oracle":     "oracle",
		"SQL Server": "sqlserver",
	})...)

	return databases
}

// detectSecurityFeatures detects security features
func (d *Detector) detectSecurityFeatures(headers map[string]string, body string) []string {
	var features []string

	// Check security headers
	securityHeaders := map[string]string{
		"x-frame-options":           "X-Frame-Options",
		"x-xss-protection":          "XSS Protection",
		"x-content-type-options":    "Content Type Options",
		"strict-transport-security": "HSTS",
		"content-security-policy":   "CSP",
		"x-csrf-token":              "CSRF Protection",
	}

	for header, feature := range securityHeaders {
		if _, exists := headers[header]; exists {
			features = append(features, feature)
		}
	}

	return features
}

// detectFrameworkVersions detects framework versions
func (d *Detector) detectFrameworkVersions(body string, headers map[string]string) map[string]string {
	versions := make(map[string]string)

	// Extract versions from meta tags and comments
	re := regexp.MustCompile(`(?i)(jquery|bootstrap|angular|react|vue)[^0-9]*([0-9]+\.[0-9]+(?:\.[0-9]+)?)`)
	matches := re.FindAllStringSubmatch(body, -1)
	for _, match := range matches {
		if len(match) > 2 {
			framework := strings.Title(strings.ToLower(match[1]))
			version := match[2]
			versions[framework] = version
		}
	}

	return versions
}

// analyzeHeaderFingerprints analyzes HTTP headers for fingerprinting
func (d *Detector) analyzeHeaderFingerprints(headers map[string]string) map[string]string {
	fingerprints := make(map[string]string)

	// Analyze specific headers
	if server, exists := headers["server"]; exists {
		fingerprints["server"] = server
	}
	if poweredBy, exists := headers["x-powered-by"]; exists {
		fingerprints["powered-by"] = poweredBy
	}
	if generator, exists := headers["x-generator"]; exists {
		fingerprints["generator"] = generator
	}

	return fingerprints
}

// analyzeCookieFingerprints analyzes cookies for fingerprinting
func (d *Detector) analyzeCookieFingerprints(cookies map[string]string) map[string]string {
	fingerprints := make(map[string]string)

	// Analyze session cookies
	for name, value := range cookies {
		nameLower := strings.ToLower(name)
		if strings.Contains(nameLower, "session") || strings.Contains(nameLower, "sess") {
			fingerprints["session-type"] = name
		}
		if strings.Contains(nameLower, "csrf") || strings.Contains(nameLower, "token") {
			fingerprints["csrf-protection"] = name
		}
		// Limit value length for fingerprinting
		if len(value) > 50 {
			fingerprints[name] = value[:50] + "..."
		} else {
			fingerprints[name] = value
		}
	}

	return fingerprints
}

// calculateConfidence calculates confidence score based on detected features
func (d *Detector) calculateConfidence(fingerprint *schema.FingerprintInfo) int {
	score := 0

	// Base score for successful detection
	score += 20

	// Add points for each detected feature
	if fingerprint.ServerType != "" {
		score += 15
	}
	if fingerprint.CMS != "" {
		score += 20
	}
	if len(fingerprint.Frameworks) > 0 {
		score += 10
	}
	if len(fingerprint.JavaScript) > 0 {
		score += 10
	}
	if len(fingerprint.Technologies) > 0 {
		score += 10
	}
	if len(fingerprint.ProgrammingLanguages) > 0 {
		score += 15
	}

	// Cap at 100
	if score > 100 {
		score = 100
	}

	return score
}

// detectInContent detects technologies in content based on keywords
func (d *Detector) detectInContent(content string, keywords map[string]string) []string {
	var detected []string
	for name, keyword := range keywords {
		if strings.Contains(content, keyword) {
			detected = append(detected, name)
		}
	}
	return detected
}

// removeDuplicates removes duplicate strings from slice
func (d *Detector) removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	return result
}
