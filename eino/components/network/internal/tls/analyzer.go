/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package tls

import (
	"context"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"net"
	"os/exec"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Analyzer provides TLS analysis capabilities
type Analyzer struct {
	timeout    time.Duration
	skipVerify bool
}

// NewAnalyzer creates a new TLS analyzer
func NewAnalyzer(timeout time.Duration, skipVerify bool) *Analyzer {
	if timeout == 0 {
		timeout = 5 * time.Second
	}
	return &Analyzer{
		timeout:    timeout,
		skipVerify: skipVerify,
	}
}

// AnalyzeTLS performs TLS analysis on a host
func (a *Analyzer) AnalyzeTLS(ctx context.Context, host string, port int) (string, *schema.TLSSecurityInfo, error) {
	if port == 0 {
		port = 443
	}

	dialTarget := net.JoinHostPort(host, fmt.Sprintf("%d", port))

	// Configure TLS
	cfg := &tls.Config{
		InsecureSkipVerify: a.skipVerify,
		ServerName:         host,
	}

	// Create dialer with timeout
	dialer := &net.Dialer{
		Timeout: a.timeout,
	}

	// Establish TLS connection
	conn, err := tls.DialWithDialer(dialer, "tcp", dialTarget, cfg)
	if err != nil {
		return "", nil, fmt.Errorf("TLS dial error: %w", err)
	}
	defer conn.Close()

	state := conn.ConnectionState()

	// Extract certificate PEM
	var pemStr string
	if len(state.PeerCertificates) == 0 {
		pemStr = "no certificate returned"
	} else {
		pemBytes := pem.EncodeToMemory(&pem.Block{
			Type:  "CERTIFICATE",
			Bytes: state.PeerCertificates[0].Raw,
		})
		pemStr = string(pemBytes)
	}

	// Analyze TLS security
	securityInfo := a.analyzeTLSSecurity(&state)

	return pemStr, securityInfo, nil
}

// analyzeTLSSecurity analyzes the TLS connection state for security information
func (a *Analyzer) analyzeTLSSecurity(state *tls.ConnectionState) *schema.TLSSecurityInfo {
	info := &schema.TLSSecurityInfo{
		CertificateInfo: make(map[string]string),
		Vulnerabilities: make(map[string]bool),
		CipherSuites:    []schema.CipherSuiteInfo{},
		Recommendations: []string{},
	}

	// TLS Version
	version := a.getTLSVersionString(state.Version)
	info.SupportedVersions = []string{version}
	info.CertificateInfo["tls_version"] = version

	// Cipher Suite
	cipherName := tls.CipherSuiteName(state.CipherSuite)
	info.CipherSuites = []schema.CipherSuiteInfo{
		{
			Name:     cipherName,
			Strength: a.getCipherStrength(state.CipherSuite),
		},
	}
	info.CertificateInfo["cipher_suite"] = cipherName

	// Certificate analysis
	if len(state.PeerCertificates) > 0 {
		cert := state.PeerCertificates[0]

		// Certificate info
		info.CertificateInfo["issuer"] = cert.Issuer.String()
		info.CertificateInfo["subject"] = cert.Subject.String()
		info.CertificateInfo["expiry_date"] = cert.NotAfter.Format(time.RFC3339)
		info.CertificateInfo["signature_algorithm"] = cert.SignatureAlgorithm.String()

		// Check for common vulnerabilities
		vulns := a.checkVulnerabilities(state, cert)
		for _, vuln := range vulns {
			info.Vulnerabilities[vuln] = true
		}

		// Security rating
		info.SecurityRating = a.calculateSecurityGrade(state, cert)

		// Generate recommendations
		info.Recommendations = a.generateRecommendations(state, cert)
	}

	info.ConnectionSuccessful = true
	return info
}

// getTLSVersionString converts TLS version number to string
func (a *Analyzer) getTLSVersionString(version uint16) string {
	switch version {
	case tls.VersionTLS10:
		return "TLS 1.0"
	case tls.VersionTLS11:
		return "TLS 1.1"
	case tls.VersionTLS12:
		return "TLS 1.2"
	case tls.VersionTLS13:
		return "TLS 1.3"
	default:
		return fmt.Sprintf("Unknown (%d)", version)
	}
}

// checkVulnerabilities checks for known TLS vulnerabilities
func (a *Analyzer) checkVulnerabilities(state *tls.ConnectionState, cert *x509.Certificate) []string {
	var vulnerabilities []string

	// Check for weak TLS versions
	if state.Version < tls.VersionTLS12 {
		vulnerabilities = append(vulnerabilities, "Weak TLS version (< 1.2)")
	}

	// Check certificate expiry
	if time.Now().After(cert.NotAfter) {
		vulnerabilities = append(vulnerabilities, "Certificate expired")
	}

	// Check if certificate expires soon (within 30 days)
	if time.Now().Add(30 * 24 * time.Hour).After(cert.NotAfter) {
		vulnerabilities = append(vulnerabilities, "Certificate expires soon")
	}

	// Check for weak signature algorithms
	if cert.SignatureAlgorithm == x509.MD5WithRSA ||
		cert.SignatureAlgorithm == x509.SHA1WithRSA {
		vulnerabilities = append(vulnerabilities, "Weak signature algorithm")
	}

	// Check for weak key sizes
	if cert.PublicKeyAlgorithm == x509.RSA {
		if rsaKey, ok := cert.PublicKey.(*rsa.PublicKey); ok {
			if rsaKey.N.BitLen() < 2048 {
				vulnerabilities = append(vulnerabilities, "Weak RSA key size (< 2048 bits)")
			}
		}
	}

	return vulnerabilities
}

// calculateSecurityGrade calculates an overall security grade
func (a *Analyzer) calculateSecurityGrade(state *tls.ConnectionState, cert *x509.Certificate) string {
	score := 100

	// Deduct points for TLS version
	if state.Version < tls.VersionTLS12 {
		score -= 30
	} else if state.Version < tls.VersionTLS13 {
		score -= 10
	}

	// Deduct points for certificate issues
	if time.Now().After(cert.NotAfter) {
		score -= 50
	} else if time.Now().Add(30 * 24 * time.Hour).After(cert.NotAfter) {
		score -= 20
	}

	// Deduct points for weak algorithms
	if cert.SignatureAlgorithm == x509.MD5WithRSA ||
		cert.SignatureAlgorithm == x509.SHA1WithRSA {
		score -= 25
	}

	// Convert score to grade
	if score >= 90 {
		return "A"
	} else if score >= 80 {
		return "B"
	} else if score >= 70 {
		return "C"
	} else if score >= 60 {
		return "D"
	} else {
		return "F"
	}
}

// GetOpenSSLInfo gets additional TLS information using OpenSSL command
func (a *Analyzer) GetOpenSSLInfo(ctx context.Context, host string, port int) (string, error) {
	if port == 0 {
		port = 443
	}

	dialTarget := net.JoinHostPort(host, fmt.Sprintf("%d", port))

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, a.timeout)
	defer cancel()

	// Run openssl s_client command
	cmd := exec.CommandContext(ctx, "openssl", "s_client",
		"-servername", host,
		"-connect", dialTarget,
		"-brief")

	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("openssl command failed: %w", err)
	}

	return string(output), nil
}

// getCipherStrength determines the strength of a cipher suite
func (a *Analyzer) getCipherStrength(cipherSuite uint16) string {
	// This is a simplified classification
	switch cipherSuite {
	case tls.TLS_AES_256_GCM_SHA384,
		tls.TLS_CHACHA20_POLY1305_SHA256,
		tls.TLS_AES_128_GCM_SHA256:
		return "Strong"
	case tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
		tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
		tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
		tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256:
		return "Strong"
	case tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
		tls.TLS_RSA_WITH_AES_128_GCM_SHA256:
		return "Medium"
	default:
		return "Weak"
	}
}

// generateRecommendations generates security recommendations
func (a *Analyzer) generateRecommendations(state *tls.ConnectionState, cert *x509.Certificate) []string {
	var recommendations []string

	// TLS version recommendations
	if state.Version < tls.VersionTLS12 {
		recommendations = append(recommendations, "Upgrade to TLS 1.2 or higher")
	} else if state.Version < tls.VersionTLS13 {
		recommendations = append(recommendations, "Consider upgrading to TLS 1.3 for better security")
	}

	// Certificate recommendations
	if time.Now().Add(30 * 24 * time.Hour).After(cert.NotAfter) {
		recommendations = append(recommendations, "Renew certificate before expiration")
	}

	// Signature algorithm recommendations
	if cert.SignatureAlgorithm == x509.MD5WithRSA ||
		cert.SignatureAlgorithm == x509.SHA1WithRSA {
		recommendations = append(recommendations, "Use stronger signature algorithm (SHA-256 or higher)")
	}

	// Key size recommendations
	if cert.PublicKeyAlgorithm == x509.RSA {
		if rsaKey, ok := cert.PublicKey.(*rsa.PublicKey); ok {
			if rsaKey.N.BitLen() < 2048 {
				recommendations = append(recommendations, "Use RSA key size of 2048 bits or higher")
			}
		}
	}

	return recommendations
}
