/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package geolocation

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Resolver 提供IP地理位置功能
type Resolver struct {
	client  *http.Client  // HTTP客户端
	timeout time.Duration // 超时时间
}

// ipAPIResponse 表示来自IP-API.com的响应
type ipAPIResponse struct {
	Status      string  `json:"status"`      // 状态
	Country     string  `json:"country"`     // 国家
	CountryCode string  `json:"countryCode"` // 国家代码
	Region      string  `json:"region"`      // 地区代码
	RegionName  string  `json:"regionName"`  // 地区名称
	City        string  `json:"city"`        // 城市
	Zip         string  `json:"zip"`         // 邮政编码
	Lat         float64 `json:"lat"`         // 纬度
	Lon         float64 `json:"lon"`         // 经度
	Timezone    string  `json:"timezone"`    // 时区
	ISP         string  `json:"isp"`         // ISP提供商
	Org         string  `json:"org"`         // 组织
	AS          string  `json:"as"`          // AS号码
	Query       string  `json:"query"`       // 查询的IP
	Message     string  `json:"message"`     // 错误消息
}

// NewResolver 创建新的地理位置解析器
func NewResolver(timeout time.Duration) *Resolver {
	return &Resolver{
		client: &http.Client{
			Timeout: timeout,
		},
		timeout: timeout,
	}
}

// ResolveIP 获取IP地址的地理位置信息
func (r *Resolver) ResolveIP(ctx context.Context, ipStr string) (*schema.GeoLocationInfo, error) {
	// 解析和验证IP地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return nil, fmt.Errorf("无效的IP地址: %s", ipStr)
	}

	// 检查是否为私有IP
	if isPrivateIP(ip) {
		return &schema.GeoLocationInfo{
			IP:          ipStr,
			Country:     "保留地址",
			CountryCode: "ZZ",
			City:        "私有网络",
		}, nil
	}

	// Query IP-API.com for geolocation information
	apiURL := fmt.Sprintf("http://ip-api.com/json/%s?fields=status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,query", ipStr)

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("IP geolocation API request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read API response: %w", err)
	}

	var apiResp ipAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to parse API response: %w", err)
	}

	// Check API response status
	if apiResp.Status != "success" {
		return nil, fmt.Errorf("IP geolocation API returned error: %s", apiResp.Message)
	}

	// Extract ASN number
	asn := ""
	if apiResp.AS != "" {
		// AS format is usually "AS12345 Some ISP", we only need the number part
		for i := 0; i < len(apiResp.AS); i++ {
			if apiResp.AS[i] >= '0' && apiResp.AS[i] <= '9' {
				asn += string(apiResp.AS[i])
			} else if apiResp.AS[i] != 'A' && apiResp.AS[i] != 'S' && apiResp.AS[i] != ' ' {
				break
			}
		}
	}

	// Create geolocation information
	geoLocation := &schema.GeoLocationInfo{
		IP:           ipStr,
		Country:      apiResp.Country,
		CountryCode:  apiResp.CountryCode,
		Region:       apiResp.RegionName,
		City:         apiResp.City,
		PostalCode:   apiResp.Zip,
		Latitude:     apiResp.Lat,
		Longitude:    apiResp.Lon,
		ISP:          apiResp.ISP,
		Organization: apiResp.Org,
		ASN:          asn,
		Timezone:     apiResp.Timezone,
	}

	return geoLocation, nil
}

// ResolveDomain gets geolocation information for a domain by resolving its IP first
func (r *Resolver) ResolveDomain(ctx context.Context, domain string) (*schema.GeoLocationInfo, error) {
	// Resolve domain to IP addresses
	ips, err := net.LookupIP(domain)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve domain %s: %w", domain, err)
	}

	if len(ips) == 0 {
		return nil, fmt.Errorf("no IP addresses found for domain %s", domain)
	}

	// Use the first IPv4 address
	var targetIP string
	for _, ip := range ips {
		if v4 := ip.To4(); v4 != nil {
			targetIP = v4.String()
			break
		}
	}

	if targetIP == "" {
		// If no IPv4, use the first IPv6
		targetIP = ips[0].String()
	}

	// Get geolocation for the resolved IP
	return r.ResolveIP(ctx, targetIP)
}

// isPrivateIP checks if an IP address is private
func isPrivateIP(ip net.IP) bool {
	// Check for IPv4 private addresses
	if ip4 := ip.To4(); ip4 != nil {
		// 10.0.0.0/8
		if ip4[0] == 10 {
			return true
		}
		// **********/12
		if ip4[0] == 172 && ip4[1] >= 16 && ip4[1] <= 31 {
			return true
		}
		// ***********/16
		if ip4[0] == 192 && ip4[1] == 168 {
			return true
		}
		// *********/8 (loopback)
		if ip4[0] == 127 {
			return true
		}
		// ***********/16 (link-local)
		if ip4[0] == 169 && ip4[1] == 254 {
			return true
		}
	}

	// Check for IPv6 local addresses
	if ip.IsLoopback() || ip.IsLinkLocalUnicast() || ip.IsLinkLocalMulticast() {
		return true
	}

	// Check for IPv6 unique local addresses (fc00::/7)
	if len(ip) == 16 && (ip[0]&0xfe) == 0xfc {
		return true
	}

	return false
}

// BatchResolveIPs resolves geolocation for multiple IP addresses concurrently
func (r *Resolver) BatchResolveIPs(ctx context.Context, ips []string) ([]*schema.GeoLocationInfo, error) {
	results := make([]*schema.GeoLocationInfo, len(ips))
	errors := make([]error, len(ips))

	// Use a semaphore to limit concurrent requests
	semaphore := make(chan struct{}, 5) // Limit to 5 concurrent requests

	// Create a channel to collect results
	type result struct {
		index int
		geo   *schema.GeoLocationInfo
		err   error
	}
	resultChan := make(chan result, len(ips))

	// Start goroutines for each IP
	for i, ip := range ips {
		go func(index int, ipAddr string) {
			semaphore <- struct{}{}        // Acquire semaphore
			defer func() { <-semaphore }() // Release semaphore

			geo, err := r.ResolveIP(ctx, ipAddr)
			resultChan <- result{index: index, geo: geo, err: err}
		}(i, ip)
	}

	// Collect results
	for i := 0; i < len(ips); i++ {
		res := <-resultChan
		results[res.index] = res.geo
		errors[res.index] = res.err
	}

	// Check if all requests failed
	allFailed := true
	for _, err := range errors {
		if err == nil {
			allFailed = false
			break
		}
	}

	if allFailed {
		return nil, fmt.Errorf("all geolocation requests failed")
	}

	return results, nil
}
