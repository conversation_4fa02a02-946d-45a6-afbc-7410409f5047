/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cdn

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Detector provides CDN detection functionality
type Detector struct {
	timeout time.Duration
	client  *http.Client
}

// NewDetector creates a new CDN detector
func NewDetector(timeout time.Duration) *Detector {
	// Create HTTP client with custom transport
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // Skip certificate verification
		},
		DisableKeepAlives: true,
		IdleConnTimeout:   5 * time.Second,
	}

	client := &http.Client{
		Timeout:   timeout,
		Transport: tr,
	}

	return &Detector{
		timeout: timeout,
		client:  client,
	}
}

// DetectCDN analyzes a domain for CDN usage
func (d *Detector) DetectCDN(ctx context.Context, domain string) (*schema.CDNInfo, error) {
	cdnInfo := &schema.CDNInfo{
		Provider:   "",
		Confidence: 0,
		Headers:    make(map[string]string),
		CNames:     []string{},
	}

	// Fetch HTTP headers
	headers, err := d.fetchHeaders(ctx, domain)
	if err != nil {
		return cdnInfo, fmt.Errorf("failed to fetch headers: %w", err)
	}

	cdnInfo.Headers = headers

	// Get CNAME records
	cnames, err := d.lookupCNAME(domain)
	if err == nil {
		cdnInfo.CNames = cnames
	}

	// Detect CDN provider
	provider, confidence := d.detectProvider(headers, cdnInfo.CNames)
	cdnInfo.Provider = provider
	cdnInfo.Confidence = confidence

	return cdnInfo, nil
}

// fetchHeaders retrieves HTTP headers from the domain
func (d *Detector) fetchHeaders(ctx context.Context, domain string) (map[string]string, error) {
	headers := make(map[string]string)

	// Try HTTPS first
	url := fmt.Sprintf("https://%s", domain)
	req, err := http.NewRequestWithContext(ctx, http.MethodHead, url, nil)
	if err != nil {
		return headers, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := d.client.Do(req)
	if err != nil {
		// HTTPS failed, try HTTP
		url = fmt.Sprintf("http://%s", domain)
		req, err = http.NewRequestWithContext(ctx, http.MethodHead, url, nil)
		if err != nil {
			return headers, err
		}
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

		resp, err = d.client.Do(req)
		if err != nil {
			return headers, err
		}
	}
	defer resp.Body.Close()

	// Extract relevant headers
	relevantHeaders := map[string]struct{}{
		"cf-ray":                 {},
		"cf-cache-status":        {},
		"x-cache":                {},
		"server":                 {},
		"via":                    {},
		"x-served-by":            {},
		"x-cache-hits":           {},
		"x-cdn":                  {},
		"x-amz-cf-id":            {},
		"x-azure-ref":            {},
		"x-varnish":              {},
		"x-fastly-request-id":    {},
		"x-akamai-transformed":   {},
		"x-iinfo":                {},
		"x-sucuri-id":            {},
		"x-swift-cachestatus":    {},
		"x-daa-tunnel":           {},
		"x-bce-cdn":              {},
		"x-ec-cache":             {},
		"x-ll-routing":           {},
		"x-sp-edge":              {},
		"x-cache-status":         {},
		"x-edge-location":        {},
		"x-powered-by":           {},
		"x-generator":            {},
		"x-drupal-cache":         {},
		"x-magento-cache":        {},
		"x-shopify-stage":        {},
		"x-wix-request-id":       {},
		"x-wordpress-cache":      {},
		"x-content-type-options": {},
		"x-xss-protection":       {},
		"x-frame-options":        {},
	}

	// Extract all headers and filter relevant ones
	for k, v := range resp.Header {
		key := strings.ToLower(k)
		value := strings.Join(v, "; ")
		headers[key] = value
	}

	// Filter to only include relevant headers
	filteredHeaders := make(map[string]string)
	for key, value := range headers {
		if _, ok := relevantHeaders[key]; ok {
			filteredHeaders[key] = value
		}
	}

	return filteredHeaders, nil
}

// lookupCNAME queries CNAME records for the domain
func (d *Detector) lookupCNAME(domain string) ([]string, error) {
	var cnames []string

	// Try to get CNAME directly
	cname, err := net.LookupCNAME(domain)
	if err == nil && cname != "" && cname != domain+"." {
		cnames = append(cnames, strings.TrimSuffix(cname, "."))
	}

	// If no direct CNAME, try NS records
	if len(cnames) == 0 {
		nss, err := net.LookupNS(domain)
		if err == nil && len(nss) > 0 {
			for _, ns := range nss {
				cname := strings.TrimSuffix(ns.Host, ".")
				if cname != domain {
					cnames = append(cnames, cname)
				}
			}
		}
	}

	return cnames, nil
}

// detectProvider detects CDN provider based on headers and CNAME records
func (d *Detector) detectProvider(headers map[string]string, cnames []string) (string, int) {
	confidence := 0
	provider := ""

	// Check headers for CDN indicators
	for header, value := range headers {
		headerLower := strings.ToLower(header)
		valueLower := strings.ToLower(value)

		// Cloudflare
		if strings.Contains(headerLower, "cf-") || strings.Contains(valueLower, "cloudflare") {
			provider = "Cloudflare"
			confidence = 90
			break
		}

		// AWS CloudFront
		if strings.Contains(headerLower, "x-amz-cf-") || strings.Contains(valueLower, "cloudfront") {
			provider = "AWS CloudFront"
			confidence = 90
			break
		}

		// Fastly
		if strings.Contains(headerLower, "x-fastly-") || strings.Contains(valueLower, "fastly") {
			provider = "Fastly"
			confidence = 90
			break
		}

		// Akamai
		if strings.Contains(headerLower, "x-akamai-") || strings.Contains(valueLower, "akamai") {
			provider = "Akamai"
			confidence = 90
			break
		}

		// Azure CDN
		if strings.Contains(headerLower, "x-azure-") || strings.Contains(valueLower, "azure") {
			provider = "Azure CDN"
			confidence = 90
			break
		}

		// MaxCDN/StackPath
		if strings.Contains(valueLower, "maxcdn") || strings.Contains(valueLower, "stackpath") {
			provider = "StackPath"
			confidence = 85
			break
		}

		// KeyCDN
		if strings.Contains(valueLower, "keycdn") {
			provider = "KeyCDN"
			confidence = 85
			break
		}

		// Generic cache indicators
		if strings.Contains(headerLower, "x-cache") && confidence < 50 {
			provider = "Unknown CDN"
			confidence = 50
		}
	}

	// Check CNAME records for CDN indicators
	for _, cname := range cnames {
		cnameLower := strings.ToLower(cname)

		if strings.Contains(cnameLower, "cloudflare") {
			provider = "Cloudflare"
			confidence = 95
			break
		}
		if strings.Contains(cnameLower, "cloudfront") {
			provider = "AWS CloudFront"
			confidence = 95
			break
		}
		if strings.Contains(cnameLower, "fastly") {
			provider = "Fastly"
			confidence = 95
			break
		}
		if strings.Contains(cnameLower, "akamai") {
			provider = "Akamai"
			confidence = 95
			break
		}
		if strings.Contains(cnameLower, "azure") {
			provider = "Azure CDN"
			confidence = 95
			break
		}
		if strings.Contains(cnameLower, "maxcdn") || strings.Contains(cnameLower, "stackpath") {
			provider = "StackPath"
			confidence = 90
			break
		}
		if strings.Contains(cnameLower, "keycdn") {
			provider = "KeyCDN"
			confidence = 90
			break
		}
	}

	return provider, confidence
}
