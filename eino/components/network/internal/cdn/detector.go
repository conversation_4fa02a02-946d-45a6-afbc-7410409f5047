/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cdn

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Provider represents a CDN provider configuration
type Provider struct {
	Name        string   // CDN provider name
	HeaderRules []string // HTTP header rules
	CNameRules  []string // CNAME rules
}

// Detector provides CDN detection capabilities
type Detector struct {
	client    *http.Client
	timeout   time.Duration
	providers []Provider
}

// NewDetector creates a new CDN detector
func NewDetector(timeout time.Duration, skipVerify bool) *Detector {
	if timeout == 0 {
		timeout = 15 * time.Second
	}

	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: skipVerify,
		},
		DisableKeepAlives: true,
		IdleConnTimeout:   5 * time.Second,
	}

	client := &http.Client{
		Timeout:   timeout,
		Transport: tr,
	}

	return &Detector{
		client:    client,
		timeout:   timeout,
		providers: getKnownProviders(),
	}
}

// DetectCDN detects CDN information for a domain
func (d *Detector) DetectCDN(ctx context.Context, domain string) (*schema.CDNInfo, error) {
	cdnInfo := &schema.CDNInfo{
		Headers: make(map[string]string),
		CNames:  []string{},
	}

	// Fetch HTTP headers
	headers, err := d.fetchHeaders(ctx, domain)
	if err != nil {
		return cdnInfo, fmt.Errorf("failed to fetch headers: %w", err)
	}

	// Extract CDN-related headers
	cdnHeaders := d.extractCDNHeaders(headers)
	cdnInfo.Headers = cdnHeaders

	// Get CNAME records
	cnames, err := d.lookupCNAME(domain)
	if err == nil {
		cdnInfo.CNames = cnames
	}

	// Detect CDN provider
	provider, confidence := d.detectProvider(headers, cnames)
	cdnInfo.Provider = provider
	cdnInfo.Confidence = confidence

	return cdnInfo, nil
}

// fetchHeaders fetches HTTP headers from the domain
func (d *Detector) fetchHeaders(ctx context.Context, domain string) (map[string]string, error) {
	headers := make(map[string]string)

	// Try HTTPS first
	url := fmt.Sprintf("https://%s", domain)
	req, err := http.NewRequestWithContext(ctx, http.MethodHead, url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := d.client.Do(req)
	if err != nil {
		// HTTPS failed, try HTTP
		url = fmt.Sprintf("http://%s", domain)
		req, err = http.NewRequestWithContext(ctx, http.MethodHead, url, nil)
		if err != nil {
			return nil, err
		}

		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

		resp, err = d.client.Do(req)
		if err != nil {
			return nil, err
		}
	}
	defer resp.Body.Close()

	// Extract all headers
	for k, v := range resp.Header {
		key := strings.ToLower(k)
		headers[key] = strings.Join(v, "; ")
	}

	return headers, nil
}

// extractCDNHeaders extracts CDN-related headers
func (d *Detector) extractCDNHeaders(headers map[string]string) map[string]string {
	cdnHeaders := make(map[string]string)

	// List of CDN-related headers to extract
	cdnHeaderNames := map[string]struct{}{
		"cf-ray":                 {},
		"cf-cache-status":        {},
		"x-cache":                {},
		"server":                 {},
		"via":                    {},
		"x-served-by":            {},
		"x-cache-hits":           {},
		"x-cdn":                  {},
		"x-amz-cf-id":            {},
		"x-azure-ref":            {},
		"x-varnish":              {},
		"x-fastly-request-id":    {},
		"x-akamai-transformed":   {},
		"x-iinfo":                {},
		"x-sucuri-id":            {},
		"x-swift-cachestatus":    {},
		"x-daa-tunnel":           {},
		"x-bce-cdn":              {},
		"x-ec-cache":             {},
		"x-ll-routing":           {},
		"x-sp-edge":              {},
		"x-cache-status":         {},
		"x-edge-location":        {},
		"x-powered-by":           {},
		"x-generator":            {},
		"x-drupal-cache":         {},
		"x-magento-cache":        {},
		"x-shopify-stage":        {},
		"x-wix-request-id":       {},
		"x-wordpress-cache":      {},
		"x-content-type-options": {},
		"x-xss-protection":       {},
		"x-frame-options":        {},
	}

	for header, value := range headers {
		if _, ok := cdnHeaderNames[header]; ok {
			cdnHeaders[header] = value
		}
	}

	return cdnHeaders
}

// lookupCNAME looks up CNAME records for a domain
func (d *Detector) lookupCNAME(domain string) ([]string, error) {
	var cnames []string

	// Try to get direct CNAME
	cname, err := net.LookupCNAME(domain)
	if err == nil && cname != "" && cname != domain+"." {
		cnames = append(cnames, strings.TrimSuffix(cname, "."))
	}

	// If no direct CNAME, try NS records
	if len(cnames) == 0 {
		nss, err := net.LookupNS(domain)
		if err == nil && len(nss) > 0 {
			for _, ns := range nss {
				cname := strings.TrimSuffix(ns.Host, ".")
				if cname != domain {
					cnames = append(cnames, cname)
				}
			}
		}
	}

	return cnames, nil
}

// detectProvider detects CDN provider based on headers and CNAME records
func (d *Detector) detectProvider(headers map[string]string, cnames []string) (provider string, confidence int) {
	confidence = 0

	// Check HTTP headers
	for _, cdnProvider := range d.providers {
		for _, headerRule := range cdnProvider.HeaderRules {
			for header, value := range headers {
				if strings.Contains(strings.ToLower(header), headerRule) ||
					strings.Contains(strings.ToLower(value), headerRule) {
					provider = cdnProvider.Name
					confidence += 50
					break
				}
			}
		}
	}

	// Check CNAME records
	if len(cnames) > 0 {
		for _, cdnProvider := range d.providers {
			for _, cnameRule := range cdnProvider.CNameRules {
				for _, cname := range cnames {
					if strings.Contains(strings.ToLower(cname), cnameRule) {
						if provider == "" || provider == cdnProvider.Name {
							provider = cdnProvider.Name
							confidence += 50
						} else {
							// Multiple CDN providers detected
							return "Multiple CDNs", 30
						}
						break
					}
				}
			}
		}
	}

	// If no specific provider detected but CDN headers present
	if provider == "" {
		cdnIndicators := []string{"x-cache", "x-served-by", "x-cache-hits", "cache-control", "via"}
		for _, indicator := range cdnIndicators {
			if _, ok := headers[indicator]; ok {
				return "Unknown CDN", 20
			}
		}
	}

	// Limit maximum confidence to 100
	if confidence > 100 {
		confidence = 100
	}

	return
}

// getKnownProviders returns a list of known CDN providers
func getKnownProviders() []Provider {
	return []Provider{
		{
			Name: "Cloudflare",
			HeaderRules: []string{
				"cf-ray",
				"cf-cache-status",
				"cloudflare",
			},
			CNameRules: []string{
				"cloudflare.com",
				"cloudflare.net",
			},
		},
		{
			Name: "Akamai",
			HeaderRules: []string{
				"x-akamai-transformed",
				"akamai",
			},
			CNameRules: []string{
				"akamai.net",
				"akamaiedge.net",
				"akamaihd.net",
				"edgesuite.net",
				"edgekey.net",
			},
		},
		{
			Name: "Fastly",
			HeaderRules: []string{
				"x-fastly",
				"fastly",
			},
			CNameRules: []string{
				"fastly.net",
			},
		},
		{
			Name: "Amazon CloudFront",
			HeaderRules: []string{
				"x-amz-cf-id",
				"cloudfront",
			},
			CNameRules: []string{
				"cloudfront.net",
			},
		},
		{
			Name: "Google Cloud CDN",
			HeaderRules: []string{
				"x-goog-",
			},
			CNameRules: []string{
				"googleusercontent.com",
			},
		},
		{
			Name: "Microsoft Azure CDN",
			HeaderRules: []string{
				"x-azure-ref",
			},
			CNameRules: []string{
				"azureedge.net",
				"msecnd.net",
			},
		},
		{
			Name: "Alibaba Cloud CDN",
			HeaderRules: []string{
				"x-swift-",
			},
			CNameRules: []string{
				"alicdn.com",
				"aliyuncs.com",
			},
		},
		{
			Name: "Tencent Cloud CDN",
			HeaderRules: []string{
				"x-daa-",
			},
			CNameRules: []string{
				"qcloud.com",
				"tencent-cloud.net",
			},
		},
		{
			Name: "Baidu Cloud CDN",
			HeaderRules: []string{
				"x-bce-",
			},
			CNameRules: []string{
				"bdydns.com",
				"bcebos.com",
			},
		},
		{
			Name: "Verizon Edgecast",
			HeaderRules: []string{
				"x-ec-",
			},
			CNameRules: []string{
				"edgecastcdn.net",
			},
		},
		{
			Name: "Limelight",
			HeaderRules: []string{
				"x-ll-",
			},
			CNameRules: []string{
				"limelight.com",
				"llnwd.net",
			},
		},
		{
			Name: "StackPath",
			HeaderRules: []string{
				"x-sp-",
			},
			CNameRules: []string{
				"stackpathdns.com",
			},
		},
		{
			Name: "Imperva Incapsula",
			HeaderRules: []string{
				"x-iinfo",
				"incap_ses",
			},
			CNameRules: []string{
				"incapdns.net",
			},
		},
		{
			Name: "Sucuri",
			HeaderRules: []string{
				"x-sucuri-",
			},
			CNameRules: []string{
				"sucuri.net",
			},
		},
		{
			Name: "Cachefly",
			HeaderRules: []string{
				"x-cf1",
			},
			CNameRules: []string{
				"cachefly.net",
			},
		},
	}
}
