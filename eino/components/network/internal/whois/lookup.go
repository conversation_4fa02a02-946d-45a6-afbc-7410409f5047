/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package whois

import (
	"context"
	"fmt"
	"os/exec"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Cache provides WHOIS caching capabilities
type Cache struct {
	cache     map[string]*CacheEntry
	mutex     sync.RWMutex
	ttl       time.Duration
	lastClean time.Time
}

// CacheEntry represents a cached WHOIS entry
type CacheEntry struct {
	Raw       string
	Company   *schema.CompanyInfo
	Success   bool
	Timestamp time.Time
}

// Lookup provides WHOIS lookup capabilities
type Lookup struct {
	cache   *Cache
	timeout time.Duration
}

// NewLookup creates a new WHOIS lookup service
func NewLookup(timeout time.Duration, cacheTTL time.Duration) *Lookup {
	if timeout == 0 {
		timeout = 10 * time.Second
	}
	if cacheTTL == 0 {
		cacheTTL = 24 * time.Hour
	}

	return &Lookup{
		cache:   NewCache(cacheTTL),
		timeout: timeout,
	}
}

// NewCache creates a new WHOIS cache
func NewCache(ttl time.Duration) *Cache {
	return &Cache{
		cache:     make(map[string]*CacheEntry),
		ttl:       ttl,
		lastClean: time.Now(),
	}
}

// Get retrieves WHOIS information from cache
func (c *Cache) Get(key string) (*CacheEntry, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, ok := c.cache[key]
	if !ok {
		return nil, false
	}

	// Check if entry is expired
	if time.Since(entry.Timestamp) > c.ttl {
		return nil, false
	}

	return entry, true
}

// Set stores WHOIS information in cache
func (c *Cache) Set(key string, raw string, company *schema.CompanyInfo, success bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.cache[key] = &CacheEntry{
		Raw:       raw,
		Company:   company,
		Success:   success,
		Timestamp: time.Now(),
	}

	// Periodically clean expired entries
	if time.Since(c.lastClean) > time.Hour {
		c.cleanExpired()
		c.lastClean = time.Now()
	}
}

// cleanExpired removes expired cache entries
func (c *Cache) cleanExpired() {
	now := time.Now()
	for key, entry := range c.cache {
		if now.Sub(entry.Timestamp) > c.ttl {
			delete(c.cache, key)
		}
	}
}

// LookupDomain performs WHOIS lookup for a domain
func (l *Lookup) LookupDomain(ctx context.Context, domain string) (*schema.WHOISInfo, error) {
	start := time.Now()

	// Check cache first
	if entry, found := l.cache.Get(domain); found {
		return &schema.WHOISInfo{
			RawWhois:       entry.Raw,
			RuntimeMs:      0, // Cache hit
			Success:        entry.Success,
			Company:        entry.Company,
			RegistrarInfo:  extractRegistrarInfo(entry.Raw),
			CreationDate:   extractCreationDate(entry.Raw),
			ExpirationDate: extractExpirationDate(entry.Raw),
		}, nil
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, l.timeout)
	defer cancel()

	// Execute WHOIS command
	cmd := exec.CommandContext(ctx, "whois", domain)
	out, err := cmd.Output()
	runtimeMs := int(time.Since(start).Milliseconds())

	raw := string(out)
	success := err == nil && len(out) > 0

	var company *schema.CompanyInfo
	if success {
		company = l.extractDomainCompanyInfo(raw)
	}

	// Cache the result
	l.cache.Set(domain, raw, company, success)

	whoisInfo := &schema.WHOISInfo{
		RawWhois:  raw,
		RuntimeMs: runtimeMs,
		Success:   success,
		Company:   company,
	}

	if success {
		whoisInfo.RegistrarInfo = extractRegistrarInfo(raw)
		whoisInfo.CreationDate = extractCreationDate(raw)
		whoisInfo.ExpirationDate = extractExpirationDate(raw)
	}

	if err != nil {
		return whoisInfo, fmt.Errorf("WHOIS lookup failed: %w", err)
	}

	return whoisInfo, nil
}

// LookupIP performs WHOIS lookup for an IP address
func (l *Lookup) LookupIP(ctx context.Context, ip string) (*schema.WHOISInfo, error) {
	start := time.Now()

	// Check cache first
	if entry, found := l.cache.Get(ip); found {
		return &schema.WHOISInfo{
			RawWhois:  entry.Raw,
			RuntimeMs: 0, // Cache hit
			Success:   entry.Success,
			Company:   entry.Company,
		}, nil
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, l.timeout)
	defer cancel()

	// Execute WHOIS command for IP
	cmd := exec.CommandContext(ctx, "whois", ip)
	out, err := cmd.Output()
	runtimeMs := int(time.Since(start).Milliseconds())

	raw := string(out)
	success := err == nil && len(out) > 0

	var company *schema.CompanyInfo
	if success {
		company = l.extractIPOrgInfo(raw)
	}

	// Cache the result
	l.cache.Set(ip, raw, company, success)

	whoisInfo := &schema.WHOISInfo{
		RawWhois:  raw,
		RuntimeMs: runtimeMs,
		Success:   success,
		Company:   company,
	}

	if err != nil {
		return whoisInfo, fmt.Errorf("IP WHOIS lookup failed: %w", err)
	}

	return whoisInfo, nil
}

// extractDomainCompanyInfo extracts company information from domain WHOIS data
func (l *Lookup) extractDomainCompanyInfo(whoisData string) *schema.CompanyInfo {
	company := &schema.CompanyInfo{}

	// Try to extract company name
	companyName := extractField(whoisData, "Registrant Organization")
	if companyName == "" {
		companyName = extractField(whoisData, "Organization")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "org")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "Registrant Name")
	}

	// Try to extract contact person
	legalPerson := extractField(whoisData, "Admin Name")
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "Registrant Name")
	}
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "Technical Contact")
	}

	// Try to extract status
	status := extractField(whoisData, "Domain Status")
	if status == "" {
		status = extractField(whoisData, "Status")
	}

	// Try to extract registration number
	regNumber := extractField(whoisData, "Registry Domain ID")
	if regNumber == "" {
		regNumber = extractField(whoisData, "Domain ID")
	}

	company.CompanyName = companyName
	company.LegalPerson = legalPerson
	company.Status = status
	company.RegistrationNumber = regNumber

	// Return nil if no information was extracted
	if companyName == "" && legalPerson == "" && status == "" && regNumber == "" {
		return nil
	}

	return company
}

// extractIPOrgInfo extracts organization information from IP WHOIS data
func (l *Lookup) extractIPOrgInfo(whoisData string) *schema.CompanyInfo {
	company := &schema.CompanyInfo{}

	// Try to extract organization name
	companyName := extractField(whoisData, "Organization")
	if companyName == "" {
		companyName = extractField(whoisData, "org-name")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "OrgName")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "descr")
	}

	// Try to extract contact person
	legalPerson := extractField(whoisData, "Admin Name")
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "admin-c")
	}
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "tech-c")
	}

	// Try to extract status
	status := extractField(whoisData, "status")
	if status == "" {
		status = extractField(whoisData, "Status")
	}

	// Try to extract network range
	regNumber := extractField(whoisData, "NetRange")
	if regNumber == "" {
		regNumber = extractField(whoisData, "inetnum")
	}
	if regNumber == "" {
		regNumber = extractField(whoisData, "CIDR")
	}

	company.CompanyName = companyName
	company.LegalPerson = legalPerson
	company.Status = status
	company.RegistrationNumber = regNumber

	// Return nil if no information was extracted
	if companyName == "" && legalPerson == "" && status == "" && regNumber == "" {
		return nil
	}

	return company
}

// extractField extracts a field value from WHOIS data
func extractField(data, fieldName string) string {
	// Create matching pattern
	pattern := fmt.Sprintf("(?i)%s[\\s]*:([^\n\r]*)", regexp.QuoteMeta(fieldName))
	re := regexp.MustCompile(pattern)

	// Find matches
	matches := re.FindStringSubmatch(data)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	return ""
}

// extractRegistrarInfo extracts registrar information
func extractRegistrarInfo(whoisData string) string {
	registrar := extractField(whoisData, "Registrar")
	if registrar == "" {
		registrar = extractField(whoisData, "Sponsoring Registrar")
	}
	return registrar
}

// extractCreationDate extracts domain creation date
func extractCreationDate(whoisData string) string {
	creationDate := extractField(whoisData, "Creation Date")
	if creationDate == "" {
		creationDate = extractField(whoisData, "Created")
	}
	if creationDate == "" {
		creationDate = extractField(whoisData, "created")
	}
	return creationDate
}

// extractExpirationDate extracts domain expiration date
func extractExpirationDate(whoisData string) string {
	expirationDate := extractField(whoisData, "Expiration Date")
	if expirationDate == "" {
		expirationDate = extractField(whoisData, "Expires")
	}
	if expirationDate == "" {
		expirationDate = extractField(whoisData, "expires")
	}
	return expirationDate
}
