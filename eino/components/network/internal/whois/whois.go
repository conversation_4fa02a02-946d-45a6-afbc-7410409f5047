/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package whois

import (
	"context"
	"fmt"
	"net"
	"os/exec"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Cache 提供WHOIS缓存功能
type Cache struct {
	cache     map[string]*CacheEntry // 缓存映射
	mutex     sync.RWMutex           // 读写锁
	ttl       time.Duration          // 生存时间
	lastClean time.Time              // 上次清理时间
}

// CacheEntry 表示缓存的WHOIS条目
type CacheEntry struct {
	Raw       string              // 原始WHOIS数据
	Company   *schema.CompanyInfo // 公司信息
	Success   bool                // 查询是否成功
	Timestamp time.Time           // 时间戳
}

// Resolver 提供WHOIS解析功能
type Resolver struct {
	cache   *Cache        // 缓存实例
	timeout time.Duration // 超时时间
}

// NewResolver 创建新的WHOIS解析器
func NewResolver(cacheTTL time.Duration, timeout time.Duration) *Resolver {
	return &Resolver{
		cache:   NewCache(cacheTTL),
		timeout: timeout,
	}
}

// NewCache 创建新的WHOIS缓存
func NewCache(ttl time.Duration) *Cache {
	return &Cache{
		cache:     make(map[string]*CacheEntry),
		ttl:       ttl,
		lastClean: time.Now(),
	}
}

// Get 从缓存中获取WHOIS信息
func (c *Cache) Get(domain string) (*CacheEntry, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, ok := c.cache[domain]
	if !ok {
		return nil, false
	}

	// 检查条目是否过期
	if time.Since(entry.Timestamp) > c.ttl {
		return nil, false
	}

	return entry, true
}

// Set 将WHOIS信息存储到缓存中
func (c *Cache) Set(domain string, raw string, company *schema.CompanyInfo, success bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.cache[domain] = &CacheEntry{
		Raw:       raw,
		Company:   company,
		Success:   success,
		Timestamp: time.Now(),
	}

	// 定期清理过期条目
	if time.Since(c.lastClean) > time.Hour {
		c.cleanExpired()
		c.lastClean = time.Now()
	}
}

// cleanExpired 移除过期的缓存条目
func (c *Cache) cleanExpired() {
	now := time.Now()
	for domain, entry := range c.cache {
		if now.Sub(entry.Timestamp) > c.ttl {
			delete(c.cache, domain)
		}
	}
}

// ResolveDomain performs WHOIS lookup for a domain
func (r *Resolver) ResolveDomain(ctx context.Context, domain string) (*schema.WHOISInfo, error) {
	start := time.Now()

	// Check cache first
	if entry, found := r.cache.Get(domain); found {
		return &schema.WHOISInfo{
			RawWhois:  entry.Raw,
			RuntimeMs: 0, // Cache hit
			Success:   entry.Success,
			Company:   entry.Company,
		}, nil
	}

	// Set timeout context
	ctx, cancel := context.WithTimeout(ctx, r.timeout)
	defer cancel()

	// Execute WHOIS command
	cmd := exec.CommandContext(ctx, "whois", domain)
	out, err := cmd.Output()
	runtimeMs := int(time.Since(start).Milliseconds())

	if err != nil || len(out) == 0 {
		whoisInfo := &schema.WHOISInfo{
			RawWhois:  string(out),
			RuntimeMs: runtimeMs,
			Success:   false,
		}
		// Cache failed result
		r.cache.Set(domain, string(out), nil, false)
		return whoisInfo, fmt.Errorf("WHOIS query failed: %w", err)
	}

	// WHOIS query successful
	raw := string(out)
	company := extractCompanyInfo(raw)

	whoisInfo := &schema.WHOISInfo{
		RawWhois:       raw,
		RuntimeMs:      runtimeMs,
		Success:        true,
		Company:        company,
		RegistrarInfo:  extractField(raw, "Registrar"),
		CreationDate:   extractField(raw, "Creation Date"),
		ExpirationDate: extractField(raw, "Expiration Date"),
	}

	// Cache successful result
	r.cache.Set(domain, raw, company, true)

	return whoisInfo, nil
}

// ResolveIP performs WHOIS lookup for an IP address
func (r *Resolver) ResolveIP(ctx context.Context, ip string) (*schema.WHOISInfo, error) {
	start := time.Now()

	// Validate IP address
	if net.ParseIP(ip) == nil {
		return nil, fmt.Errorf("invalid IP address: %s", ip)
	}

	// Check cache first
	if entry, found := r.cache.Get(ip); found {
		return &schema.WHOISInfo{
			RawWhois:  entry.Raw,
			RuntimeMs: 0, // Cache hit
			Success:   entry.Success,
			Company:   entry.Company,
		}, nil
	}

	// Set timeout context
	ctx, cancel := context.WithTimeout(ctx, r.timeout)
	defer cancel()

	// Execute WHOIS command for IP
	cmd := exec.CommandContext(ctx, "whois", ip)
	out, err := cmd.Output()
	runtimeMs := int(time.Since(start).Milliseconds())

	if err != nil || len(out) == 0 {
		whoisInfo := &schema.WHOISInfo{
			RawWhois:  string(out),
			RuntimeMs: runtimeMs,
			Success:   false,
		}
		// Cache failed result
		r.cache.Set(ip, string(out), nil, false)
		return whoisInfo, fmt.Errorf("IP WHOIS query failed: %w", err)
	}

	// WHOIS query successful
	raw := string(out)
	company := extractIPOrgInfo(raw)

	whoisInfo := &schema.WHOISInfo{
		RawWhois:  raw,
		RuntimeMs: runtimeMs,
		Success:   true,
		Company:   company,
	}

	// Cache successful result
	r.cache.Set(ip, raw, company, true)

	return whoisInfo, nil
}

// extractCompanyInfo extracts company information from domain WHOIS data
func extractCompanyInfo(whoisData string) *schema.CompanyInfo {
	company := &schema.CompanyInfo{}

	// Extract organization/company name
	orgPatterns := []string{
		"Organization",
		"Registrant Organization",
		"org",
		"Company",
	}

	for _, pattern := range orgPatterns {
		if value := extractField(whoisData, pattern); value != "" {
			company.CompanyName = value
			break
		}
	}

	// Extract registrant name (legal person)
	namePatterns := []string{
		"Registrant Name",
		"Registrant",
		"name",
	}

	for _, pattern := range namePatterns {
		if value := extractField(whoisData, pattern); value != "" {
			company.LegalPerson = value
			break
		}
	}

	// Extract status
	if status := extractField(whoisData, "Status"); status != "" {
		company.Status = status
	}

	// If no company info found, return nil
	if company.CompanyName == "" && company.LegalPerson == "" {
		return nil
	}

	return company
}

// extractIPOrgInfo extracts organization information from IP WHOIS data
func extractIPOrgInfo(whoisData string) *schema.CompanyInfo {
	company := &schema.CompanyInfo{}

	// Extract organization name
	orgPatterns := []string{
		"OrgName",
		"Organization",
		"org-name",
		"descr",
	}

	for _, pattern := range orgPatterns {
		if value := extractField(whoisData, pattern); value != "" {
			company.CompanyName = value
			break
		}
	}

	// If no organization info found, return nil
	if company.CompanyName == "" {
		return nil
	}

	return company
}

// extractField extracts a field value from WHOIS data
func extractField(data, fieldName string) string {
	// Create matching pattern
	pattern := fmt.Sprintf("(?i)%s[\\s]*:([^\n\r]*)", regexp.QuoteMeta(fieldName))
	re := regexp.MustCompile(pattern)

	// Find matches
	matches := re.FindStringSubmatch(data)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	return ""
}
