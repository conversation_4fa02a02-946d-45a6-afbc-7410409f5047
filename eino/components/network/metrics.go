/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"sync"
	"sync/atomic"
	"time"
)

// MetricsCollector 指标收集器
type MetricsCollector struct {
	// 计数器
	totalRequests      int64 // 总请求数
	successfulRequests int64 // 成功请求数
	failedRequests     int64 // 失败请求数

	// 模块计数器
	dnsAnalysisCount   int64 // DNS分析次数
	whoisAnalysisCount int64 // WHOIS分析次数
	tlsAnalysisCount   int64 // TLS分析次数
	cdnAnalysisCount   int64 // CDN分析次数
	geoAnalysisCount   int64 // 地理位置分析次数
	fingerprintCount   int64 // 指纹识别次数
	aiAnalysisCount    int64 // AI分析次数

	// 错误计数器
	dnsErrors         int64 // DNS错误数
	whoisErrors       int64 // WHOIS错误数
	tlsErrors         int64 // TLS错误数
	cdnErrors         int64 // CDN错误数
	geoErrors         int64 // 地理位置错误数
	fingerprintErrors int64 // 指纹识别错误数
	aiErrors          int64 // AI分析错误数

	// 性能指标
	responseTimes      []time.Duration // 响应时间列表
	responseTimesMutex sync.RWMutex    // 响应时间锁

	// 威胁检测指标
	threatsDetected     int64        // 检测到的威胁数
	highRiskDomains     int64        // 高风险域名数
	securityScores      []int        // 安全评分列表
	securityScoresMutex sync.RWMutex // 安全评分锁

	// 缓存指标
	cacheHits   int64 // 缓存命中数
	cacheMisses int64 // 缓存未命中数

	// 并发指标
	concurrentRequests int64 // 当前并发请求数
	maxConcurrency     int64 // 最大并发数

	// 时间窗口指标
	windowSize    time.Duration
	windowMetrics map[time.Time]*WindowMetrics
	windowMutex   sync.RWMutex

	// 配置
	config    *MetricsConfig
	startTime time.Time
}

// WindowMetrics 时间窗口指标
type WindowMetrics struct {
	Timestamp       time.Time `json:"timestamp"`
	RequestCount    int64     `json:"request_count"`
	SuccessCount    int64     `json:"success_count"`
	ErrorCount      int64     `json:"error_count"`
	AvgResponseTime float64   `json:"avg_response_time"`
	ThreatCount     int64     `json:"threat_count"`
}

// MetricsConfig 指标配置
type MetricsConfig struct {
	EnableDetailedMetrics bool          `json:"enable_detailed_metrics"` // 启用详细指标
	WindowSize            time.Duration `json:"window_size"`             // 时间窗口大小
	MaxWindows            int           `json:"max_windows"`             // 最大窗口数
	CleanupInterval       time.Duration `json:"cleanup_interval"`        // 清理间隔
}

// MetricsSnapshot 指标快照
type MetricsSnapshot struct {
	// 基础指标
	TotalRequests      int64   `json:"total_requests"`
	SuccessfulRequests int64   `json:"successful_requests"`
	FailedRequests     int64   `json:"failed_requests"`
	SuccessRate        float64 `json:"success_rate"`

	// 性能指标
	AvgResponseTime float64 `json:"avg_response_time_ms"`
	MinResponseTime float64 `json:"min_response_time_ms"`
	MaxResponseTime float64 `json:"max_response_time_ms"`
	P95ResponseTime float64 `json:"p95_response_time_ms"`

	// 模块指标
	ModuleMetrics map[string]int64 `json:"module_metrics"`
	ModuleErrors  map[string]int64 `json:"module_errors"`

	// 威胁检测指标
	ThreatsDetected  int64   `json:"threats_detected"`
	HighRiskDomains  int64   `json:"high_risk_domains"`
	AvgSecurityScore float64 `json:"avg_security_score"`

	// 缓存指标
	CacheHitRate float64 `json:"cache_hit_rate"`
	CacheHits    int64   `json:"cache_hits"`
	CacheMisses  int64   `json:"cache_misses"`

	// 并发指标
	ConcurrentRequests int64 `json:"concurrent_requests"`
	MaxConcurrency     int64 `json:"max_concurrency"`

	// 运行时指标
	Uptime    float64   `json:"uptime_seconds"`
	Timestamp time.Time `json:"timestamp"`
}

// DefaultMetricsConfig 返回默认指标配置
func DefaultMetricsConfig() *MetricsConfig {
	return &MetricsConfig{
		EnableDetailedMetrics: true,
		WindowSize:            time.Minute,
		MaxWindows:            60, // 保留1小时的数据
		CleanupInterval:       5 * time.Minute,
	}
}

// NewMetricsCollector 创建新的指标收集器
func NewMetricsCollector(config *MetricsConfig) *MetricsCollector {
	if config == nil {
		config = DefaultMetricsConfig()
	}

	mc := &MetricsCollector{
		windowSize:    config.WindowSize,
		windowMetrics: make(map[time.Time]*WindowMetrics),
		config:        config,
		startTime:     time.Now(),
	}

	// 启动清理协程
	if config.EnableDetailedMetrics {
		go mc.cleanupRoutine()
	}

	return mc
}

// RecordRequest 记录请求
func (mc *MetricsCollector) RecordRequest() {
	atomic.AddInt64(&mc.totalRequests, 1)
	atomic.AddInt64(&mc.concurrentRequests, 1)

	// 更新最大并发数
	current := atomic.LoadInt64(&mc.concurrentRequests)
	for {
		max := atomic.LoadInt64(&mc.maxConcurrency)
		if current <= max {
			break
		}
		if atomic.CompareAndSwapInt64(&mc.maxConcurrency, max, current) {
			break
		}
	}
}

// RecordSuccess 记录成功
func (mc *MetricsCollector) RecordSuccess(responseTime time.Duration) {
	atomic.AddInt64(&mc.successfulRequests, 1)
	atomic.AddInt64(&mc.concurrentRequests, -1)

	// 记录响应时间
	if mc.config.EnableDetailedMetrics {
		mc.responseTimesMutex.Lock()
		mc.responseTimes = append(mc.responseTimes, responseTime)
		// 限制响应时间列表大小
		if len(mc.responseTimes) > 10000 {
			mc.responseTimes = mc.responseTimes[1000:]
		}
		mc.responseTimesMutex.Unlock()
	}

	// 更新时间窗口指标
	mc.updateWindowMetrics(true, responseTime)
}

// RecordFailure 记录失败
func (mc *MetricsCollector) RecordFailure() {
	atomic.AddInt64(&mc.failedRequests, 1)
	atomic.AddInt64(&mc.concurrentRequests, -1)

	// 更新时间窗口指标
	mc.updateWindowMetrics(false, 0)
}

// RecordModuleAnalysis 记录模块分析
func (mc *MetricsCollector) RecordModuleAnalysis(module string) {
	switch module {
	case "dns":
		atomic.AddInt64(&mc.dnsAnalysisCount, 1)
	case "whois":
		atomic.AddInt64(&mc.whoisAnalysisCount, 1)
	case "tls":
		atomic.AddInt64(&mc.tlsAnalysisCount, 1)
	case "cdn":
		atomic.AddInt64(&mc.cdnAnalysisCount, 1)
	case "geo":
		atomic.AddInt64(&mc.geoAnalysisCount, 1)
	case "fingerprint":
		atomic.AddInt64(&mc.fingerprintCount, 1)
	case "ai":
		atomic.AddInt64(&mc.aiAnalysisCount, 1)
	}
}

// RecordModuleError 记录模块错误
func (mc *MetricsCollector) RecordModuleError(module string) {
	switch module {
	case "dns":
		atomic.AddInt64(&mc.dnsErrors, 1)
	case "whois":
		atomic.AddInt64(&mc.whoisErrors, 1)
	case "tls":
		atomic.AddInt64(&mc.tlsErrors, 1)
	case "cdn":
		atomic.AddInt64(&mc.cdnErrors, 1)
	case "geo":
		atomic.AddInt64(&mc.geoErrors, 1)
	case "fingerprint":
		atomic.AddInt64(&mc.fingerprintErrors, 1)
	case "ai":
		atomic.AddInt64(&mc.aiErrors, 1)
	}
}

// RecordThreatDetection 记录威胁检测
func (mc *MetricsCollector) RecordThreatDetection(isHighRisk bool) {
	atomic.AddInt64(&mc.threatsDetected, 1)
	if isHighRisk {
		atomic.AddInt64(&mc.highRiskDomains, 1)
	}
}

// RecordSecurityScore 记录安全评分
func (mc *MetricsCollector) RecordSecurityScore(score int) {
	if mc.config.EnableDetailedMetrics {
		mc.securityScoresMutex.Lock()
		mc.securityScores = append(mc.securityScores, score)
		// 限制评分列表大小
		if len(mc.securityScores) > 10000 {
			mc.securityScores = mc.securityScores[1000:]
		}
		mc.securityScoresMutex.Unlock()
	}
}

// RecordCacheHit 记录缓存命中
func (mc *MetricsCollector) RecordCacheHit() {
	atomic.AddInt64(&mc.cacheHits, 1)
}

// RecordCacheMiss 记录缓存未命中
func (mc *MetricsCollector) RecordCacheMiss() {
	atomic.AddInt64(&mc.cacheMisses, 1)
}

// GetSnapshot 获取指标快照
func (mc *MetricsCollector) GetSnapshot() *MetricsSnapshot {
	snapshot := &MetricsSnapshot{
		TotalRequests:      atomic.LoadInt64(&mc.totalRequests),
		SuccessfulRequests: atomic.LoadInt64(&mc.successfulRequests),
		FailedRequests:     atomic.LoadInt64(&mc.failedRequests),
		ThreatsDetected:    atomic.LoadInt64(&mc.threatsDetected),
		HighRiskDomains:    atomic.LoadInt64(&mc.highRiskDomains),
		CacheHits:          atomic.LoadInt64(&mc.cacheHits),
		CacheMisses:        atomic.LoadInt64(&mc.cacheMisses),
		ConcurrentRequests: atomic.LoadInt64(&mc.concurrentRequests),
		MaxConcurrency:     atomic.LoadInt64(&mc.maxConcurrency),
		Uptime:             time.Since(mc.startTime).Seconds(),
		Timestamp:          time.Now(),
	}

	// 计算成功率
	if snapshot.TotalRequests > 0 {
		snapshot.SuccessRate = float64(snapshot.SuccessfulRequests) / float64(snapshot.TotalRequests) * 100
	}

	// 计算缓存命中率
	totalCacheRequests := snapshot.CacheHits + snapshot.CacheMisses
	if totalCacheRequests > 0 {
		snapshot.CacheHitRate = float64(snapshot.CacheHits) / float64(totalCacheRequests) * 100
	}

	// 计算响应时间统计
	if mc.config.EnableDetailedMetrics {
		mc.responseTimesMutex.RLock()
		if len(mc.responseTimes) > 0 {
			snapshot.AvgResponseTime, snapshot.MinResponseTime,
				snapshot.MaxResponseTime, snapshot.P95ResponseTime = mc.calculateResponseTimeStats()
		}
		mc.responseTimesMutex.RUnlock()

		// 计算平均安全评分
		mc.securityScoresMutex.RLock()
		if len(mc.securityScores) > 0 {
			total := 0
			for _, score := range mc.securityScores {
				total += score
			}
			snapshot.AvgSecurityScore = float64(total) / float64(len(mc.securityScores))
		}
		mc.securityScoresMutex.RUnlock()
	}

	// 模块指标
	snapshot.ModuleMetrics = map[string]int64{
		"dns":         atomic.LoadInt64(&mc.dnsAnalysisCount),
		"whois":       atomic.LoadInt64(&mc.whoisAnalysisCount),
		"tls":         atomic.LoadInt64(&mc.tlsAnalysisCount),
		"cdn":         atomic.LoadInt64(&mc.cdnAnalysisCount),
		"geo":         atomic.LoadInt64(&mc.geoAnalysisCount),
		"fingerprint": atomic.LoadInt64(&mc.fingerprintCount),
		"ai":          atomic.LoadInt64(&mc.aiAnalysisCount),
	}

	// 模块错误
	snapshot.ModuleErrors = map[string]int64{
		"dns":         atomic.LoadInt64(&mc.dnsErrors),
		"whois":       atomic.LoadInt64(&mc.whoisErrors),
		"tls":         atomic.LoadInt64(&mc.tlsErrors),
		"cdn":         atomic.LoadInt64(&mc.cdnErrors),
		"geo":         atomic.LoadInt64(&mc.geoErrors),
		"fingerprint": atomic.LoadInt64(&mc.fingerprintErrors),
		"ai":          atomic.LoadInt64(&mc.aiErrors),
	}

	return snapshot
}

// updateWindowMetrics 更新时间窗口指标
func (mc *MetricsCollector) updateWindowMetrics(success bool, responseTime time.Duration) {
	if !mc.config.EnableDetailedMetrics {
		return
	}

	now := time.Now()
	windowStart := now.Truncate(mc.windowSize)

	mc.windowMutex.Lock()
	defer mc.windowMutex.Unlock()

	window, exists := mc.windowMetrics[windowStart]
	if !exists {
		window = &WindowMetrics{
			Timestamp: windowStart,
		}
		mc.windowMetrics[windowStart] = window
	}

	window.RequestCount++
	if success {
		window.SuccessCount++
		// 更新平均响应时间
		if window.SuccessCount == 1 {
			window.AvgResponseTime = float64(responseTime.Milliseconds())
		} else {
			window.AvgResponseTime = (window.AvgResponseTime*float64(window.SuccessCount-1) +
				float64(responseTime.Milliseconds())) / float64(window.SuccessCount)
		}
	} else {
		window.ErrorCount++
	}
}

// calculateResponseTimeStats 计算响应时间统计
func (mc *MetricsCollector) calculateResponseTimeStats() (avg, min, max, p95 float64) {
	if len(mc.responseTimes) == 0 {
		return 0, 0, 0, 0
	}

	// 计算平均值、最小值、最大值
	total := time.Duration(0)
	min = float64(mc.responseTimes[0].Milliseconds())
	max = float64(mc.responseTimes[0].Milliseconds())

	for _, rt := range mc.responseTimes {
		total += rt
		rtMs := float64(rt.Milliseconds())
		if rtMs < min {
			min = rtMs
		}
		if rtMs > max {
			max = rtMs
		}
	}

	avg = float64(total.Milliseconds()) / float64(len(mc.responseTimes))

	// 计算P95
	// 简化实现，实际应该排序后取95%位置的值
	p95 = avg * 1.5 // 简化估算

	return avg, min, max, p95
}

// cleanupRoutine 清理过期的时间窗口数据
func (mc *MetricsCollector) cleanupRoutine() {
	ticker := time.NewTicker(mc.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		mc.windowMutex.Lock()
		cutoff := time.Now().Add(-time.Duration(mc.config.MaxWindows) * mc.windowSize)
		for timestamp := range mc.windowMetrics {
			if timestamp.Before(cutoff) {
				delete(mc.windowMetrics, timestamp)
			}
		}
		mc.windowMutex.Unlock()
	}
}

// GetWindowMetrics 获取时间窗口指标
func (mc *MetricsCollector) GetWindowMetrics() []*WindowMetrics {
	if !mc.config.EnableDetailedMetrics {
		return nil
	}

	mc.windowMutex.RLock()
	defer mc.windowMutex.RUnlock()

	metrics := make([]*WindowMetrics, 0, len(mc.windowMetrics))
	for _, window := range mc.windowMetrics {
		metrics = append(metrics, window)
	}

	return metrics
}
